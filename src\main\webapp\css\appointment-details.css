/* Appointment Details Page Specific Styles */

/* Fix for the main content area */
.dashboard-container {
    position: relative;
    display: flex;
    width: 100%;
    min-height: 100vh;
}

/* Ensure sidebar stays fixed */
.sidebar {
    position: fixed;
    width: 280px;
    height: 100vh;
    z-index: 100;
    left: 0;
    top: 0;
}

/* Main content positioning */
.dashboard-main {
    width: calc(100% - 280px);
    margin-left: 280px;
    position: relative;
    min-height: 100vh;
    box-sizing: border-box;
    overflow-x: hidden;
}

/* Content area styling */
.dashboard-content {
    padding: 20px;
    width: 100%;
    box-sizing: border-box;
}

/* Header styling */
.content-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    width: 100%;
}

/* Appointment details container */
.appointment-details-container {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
    overflow: hidden;
    width: 100%;
    box-sizing: border-box;
}

/* Appointment details header */
.appointment-details-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.appointment-details-header h2 {
    margin: 0;
    color: #333;
    font-size: 1.5rem;
}

/* Appointment details content */
.appointment-details-content {
    padding: 20px;
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

/* Detail items */
.detail-item {
    margin-bottom: 15px;
}

.detail-item label {
    display: block;
    font-weight: 600;
    color: #555;
    margin-bottom: 5px;
    font-size: 0.9rem;
}

.detail-item p {
    margin: 0;
    color: #333;
    font-size: 1rem;
}

/* Status badges */
.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: uppercase;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-approved {
    background-color: #d4edda;
    color: #155724;
}

.status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
}

.status-completed {
    background-color: #cce5ff;
    color: #004085;
}

/* Action buttons */
.appointment-actions {
    display: flex;
    gap: 10px;
    margin: 20px;
    justify-content: flex-start;
}

.appointment-actions .btn {
    min-width: 140px;
}

.appointment-actions .btn-danger {
    min-width: 180px;
}

/* Button styles */
.btn {
    padding: 10px 20px;
    border-radius: 8px;
    font-weight: 500;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
}

.btn i {
    margin-right: 8px;
}

.btn-primary {
    background-color: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background-color: #45a049;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-danger {
    background-color: #f44336;
    color: white;
}

.btn-danger:hover {
    background-color: #d32f2f;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.btn-outline {
    background-color: transparent;
    color: #333;
    border: 1px solid #ddd;
}

.btn-outline:hover {
    background-color: #f5f5f5;
}

/* User image styles */
.user-image {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 10px;
    border: 2px solid #f0f0f0;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Modal styles override */
.modal {
    z-index: 2000 !important; /* Ensure modal appears above sidebar */
    position: fixed;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
}

.modal-content {
    position: relative;
    margin: 5% auto;
    width: 90%;
    max-width: 500px;
}

/* Loading overlay */
.loading-overlay {
    z-index: 2100 !important; /* Ensure loading overlay appears above modal */
}

/* Responsive styles */
@media (max-width: 992px) {
    .sidebar {
        width: 240px;
    }

    .dashboard-main {
        width: calc(100% - 240px);
        margin-left: 240px;
    }
}

@media (max-width: 768px) {
    .sidebar {
        position: relative;
        width: 100%;
        height: auto;
    }

    .dashboard-main {
        width: 100%;
        margin-left: 0;
    }

    .content-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .appointment-actions {
        flex-direction: column;
        width: calc(100% - 40px);
    }

    .appointment-actions .btn {
        width: 100%;
    }
}
