/* Authentication Pages Styles */

.auth-container {
    display: flex;
    min-height: 100vh;
    background-color: #f5f5f5;
}

.auth-left {
    flex: 1;
    display: none;
}

.auth-right {
    flex: 1;
    padding: 40px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    max-width: 500px;
    margin: 0 auto;
}

.auth-image {
    height: 100%;
    position: relative;
}

.auth-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.auth-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(rgba(0, 0, 0, 0.5), rgba(0, 0, 0, 0.7));
    display: flex;
    flex-direction: column;
    justify-content: center;
    padding: 40px;
    color: white;
}

.auth-overlay h3 {
    font-size: 2rem;
    margin-bottom: 15px;
}

.auth-right h2 {
    font-size: 2rem;
    margin-bottom: 10px;
    color: var(--dark-color);
}

.auth-subtitle {
    color: var(--text-light);
    margin-bottom: 30px;
}

.auth-links {
    margin-top: 20px;
    text-align: center;
}

.social-login {
    margin-top: 30px;
    text-align: center;
}

.social-login p {
    position: relative;
    margin-bottom: 20px;
    color: var(--text-light);
}

.social-login p::before,
.social-login p::after {
    content: '';
    position: absolute;
    top: 50%;
    width: 40%;
    height: 1px;
    background-color: var(--border-color);
}

.social-login p::before {
    left: 0;
}

.social-login p::after {
    right: 0;
}

.social-btn {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 5px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-family: inherit;
    font-size: 1rem;
    cursor: pointer;
    transition: all 0.3s;
}

.social-btn i {
    margin-right: 10px;
}

.google-btn {
    background-color: white;
    color: #333;
    border: 1px solid #ddd;
}

.google-btn:hover {
    background-color: #f1f1f1;
}

.facebook-btn {
    background-color: #3b5998;
    color: white;
}

.facebook-btn:hover {
    background-color: #2d4373;
}

/* User Type Selector */
.user-type-selector {
    display: flex;
    margin-bottom: 30px;
    border-radius: 5px;
    overflow: hidden;
    border: 1px solid var(--border-color);
}

.user-type-option {
    flex: 1;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
    background-color: white;
}

.user-type-option.active {
    background-color: var(--primary-color);
    color: white;
}

.user-type-option i {
    margin-right: 5px;
}

/* User Type Forms */
.user-type-form {
    display: none;
}

.user-type-form.active {
    display: block;
}

/* Alert styles */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 5px;
    display: flex;
    align-items: center;
}

.alert i {
    margin-right: 10px;
    font-size: 1.2rem;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeeba;
}

.alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* Responsive Styles */
@media (min-width: 992px) {
    .auth-left {
        display: block;
    }
}

@media (max-width: 576px) {
    .auth-right {
        padding: 20px;
    }
}
