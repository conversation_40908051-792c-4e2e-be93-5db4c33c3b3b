/* Dashboard Layout */
.dashboard-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    width: 280px;
    background-color: #fff;
    box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    height: 100vh;
    overflow-y: auto;
    z-index: 100;
}

.user-profile {
    padding: 30px 20px;
    text-align: center;
    border-bottom: 1px solid #eee;
}

.profile-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 15px;
    border: 3px solid #f0f0f0;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* Error handling for images */
.profile-image img[src=""],
.profile-image img:not([src]) {
    display: none;
}

.profile-initials {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #4CAF50;
    color: white;
    font-size: 1.4rem;
    font-weight: 600;
}

.user-name {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.user-email, .user-phone {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 5px;
    word-break: break-word;
}

.sidebar-menu {
    padding: 20px 0;
}

.sidebar-menu li {
    margin-bottom: 5px;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #555;
    transition: all 0.3s ease;
}

.sidebar-menu a:hover, .sidebar-menu a.active {
    background-color: #f5f5f5;
    color: #4CAF50;
    border-left: 4px solid #4CAF50;
}

.sidebar-menu a i {
    margin-right: 10px;
    font-size: 1.2rem;
}

.logout-btn {
    padding: 20px;
    border-top: 1px solid #eee;
}

.logout-btn a {
    display: flex;
    align-items: center;
    color: #F44336;
    font-weight: 500;
}

.logout-btn a i {
    margin-right: 10px;
    font-size: 1.2rem;
}

/* Main Content Styles */
.main-content {
    flex: 1;
    margin-left: 280px;
    padding: 30px;
}

/* Dashboard Main Container */
.dashboard-main {
    flex: 1;
    margin-left: 280px;
    width: calc(100% - 280px);
    position: relative;
}

.dashboard-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.welcome-text h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
}

.welcome-text p {
    color: #666;
}

.dashboard-actions {
    display: flex;
    gap: 10px;
}

/* Stats Cards */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.5rem;
}

.stat-icon.green {
    background-color: rgba(76, 175, 80, 0.1);
    color: #4CAF50;
}

.stat-icon.blue {
    background-color: rgba(33, 150, 243, 0.1);
    color: #2196F3;
}

.stat-icon.orange {
    background-color: rgba(255, 152, 0, 0.1);
    color: #FF9800;
}

.stat-icon.red {
    background-color: rgba(244, 67, 54, 0.1);
    color: #F44336;
}

.stat-info h3 {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.stat-info p {
    color: #666;
    font-size: 0.9rem;
}

/* Appointment Cards */
.appointments-container {
    margin-bottom: 30px;
}

.section-title {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: #333;
    display: flex;
    align-items: center;
}

.section-title i {
    margin-right: 10px;
    color: #4CAF50;
}

.appointment-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.appointment-card {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    transition: all 0.3s ease;
    border-left: 4px solid #4CAF50;
    margin-bottom: 20px;
}

.appointment-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.appointment-card[data-status="PENDING"] {
    border-left-color: #FF9800;
}

.appointment-card[data-status="CANCELLED"] {
    border-left-color: #F44336;
}

.appointment-card[data-status="COMPLETED"] {
    border-left-color: #2196F3;
}

.appointment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.appointment-date {
    font-size: 0.9rem;
    color: #666;
}

.appointment-status {
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    display: inline-block;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.appointment-status[data-status="CONFIRMED"],
.appointment-status[data-status="APPROVED"],
.appointment-status[data-status="ACTIVE"] {
    background-color: rgba(76, 175, 80, 0.15);
    color: #2E7D32;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

.appointment-status[data-status="PENDING"] {
    background-color: rgba(255, 152, 0, 0.15);
    color: #E65100;
    border: 1px solid rgba(255, 152, 0, 0.3);
}

.appointment-status[data-status="CANCELLED"],
.appointment-status[data-status="REJECTED"] {
    background-color: rgba(244, 67, 54, 0.15);
    color: #C62828;
    border: 1px solid rgba(244, 67, 54, 0.3);
}

.appointment-status[data-status="COMPLETED"] {
    background-color: rgba(33, 150, 243, 0.15);
    color: #0D47A1;
    border: 1px solid rgba(33, 150, 243, 0.3);
}

.appointment-doctor {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.doctor-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
}

.doctor-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.doctor-info h4 {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 5px;
}

.doctor-info p {
    font-size: 0.9rem;
    color: #666;
}

.appointment-details {
    margin-bottom: 15px;
}

.appointment-detail {
    display: flex;
    margin-bottom: 10px;
}

.detail-label {
    width: 40%;
    font-weight: 500;
    color: #555;
}

.detail-value {
    width: 60%;
    color: #333;
}

.appointment-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
}

.appointment-actions button,
.appointment-actions .action-btn {
    flex: 1;
    padding: 10px 15px;
    font-size: 0.9rem;
    border-radius: 5px;
    text-align: center;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.appointment-actions .action-btn i {
    margin-right: 5px;
}

.appointment-actions .action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

/* Empty State */
.empty-state {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 30px;
    text-align: center;
}

.empty-state i {
    font-size: 3rem;
    color: #ccc;
    margin-bottom: 20px;
}

.empty-state h3 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.empty-state p {
    color: #666;
    margin-bottom: 20px;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .sidebar {
        width: 240px;
    }

    .main-content {
        margin-left: 240px;
    }

    .dashboard-main {
        margin-left: 240px;
        width: calc(100% - 240px);
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .user-profile {
        padding: 20px;
    }

    .profile-image {
        width: 80px;
        height: 80px;
        border-width: 4px;
    }

    .sidebar-menu {
        display: flex;
        flex-wrap: wrap;
        padding: 10px;
    }

    .sidebar-menu li {
        width: 50%;
    }

    .sidebar-menu a {
        padding: 10px;
    }

    .logout-btn {
        padding: 10px;
    }

    .main-content {
        margin-left: 0;
        padding: 20px;
    }

    .dashboard-main {
        margin-left: 0;
        width: 100%;
    }

    .stats-container, .appointment-cards {
        grid-template-columns: 1fr;
    }
}
