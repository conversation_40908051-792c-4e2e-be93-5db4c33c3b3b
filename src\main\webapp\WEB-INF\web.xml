<?xml version="1.0" encoding="UTF-8"?>
<web-app xmlns="https://jakarta.ee/xml/ns/jakartaee"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="https://jakarta.ee/xml/ns/jakartaee https://jakarta.ee/xml/ns/jakartaee/web-app_5_0.xsd"
         version="5.0">

    <display-name>Doctor Appointment System</display-name>

    <!-- Welcome File List -->
    <welcome-file-list>
        <welcome-file>index.jsp</welcome-file>
        <welcome-file>index.html</welcome-file>
    </welcome-file-list>

    <!-- Default Servlet for static resources -->
    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>*.css</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>*.js</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>*.png</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>*.jpg</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>*.jpeg</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>*.gif</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>*.svg</url-pattern>
    </servlet-mapping>
    <servlet-mapping>
        <servlet-name>default</servlet-name>
        <url-pattern>*.ico</url-pattern>
    </servlet-mapping>


    <!-- Error Pages -->
    <error-page>
        <error-code>404</error-code>
        <location>/404.jsp</location>
    </error-page>

    <error-page>
        <error-code>500</error-code>
        <location>/error.jsp</location>
    </error-page>

    <error-page>
        <error-code>503</error-code>
        <location>/error.jsp</location>
    </error-page>

    <error-page>
        <exception-type>java.lang.Exception</exception-type>
        <location>/error.jsp</location>
    </error-page>

    <!-- Session Configuration -->
    <session-config>
        <session-timeout>30</session-timeout>
    </session-config>

    <!-- Connection Timeout Configuration -->
    <servlet>
        <servlet-name>default</servlet-name>
        <init-param>
            <param-name>connectionTimeout</param-name>
            <param-value>60000</param-value>
        </init-param>
    </servlet>

    <!-- MIME Type Mappings -->
    <mime-mapping>
        <extension>svg</extension>
        <mime-type>image/svg+xml</mime-type>
    </mime-mapping>

    <!-- JSTL Configuration -->
    <jsp-config>
        <taglib>
            <taglib-uri>http://doctorapp.com/tags</taglib-uri>
            <taglib-location>/WEB-INF/doctorapp.tld</taglib-location>
        </taglib>
    </jsp-config>

    <!-- Filters -->
    <filter>
        <filter-name>SessionFilter</filter-name>
        <filter-class>com.doctorapp.filter.SessionFilter</filter-class>
    </filter>
    <filter-mapping>
        <filter-name>SessionFilter</filter-name>
        <url-pattern>/*</url-pattern>
    </filter-mapping>

    <!-- Listeners -->
    <listener>
        <listener-class>com.doctorapp.util.AppInitializer</listener-class>
    </listener>
    <listener>
        <listener-class>com.doctorapp.util.DatabaseConnectionListener</listener-class>
    </listener>

    <!-- Multipart Configuration -->
    <servlet>
        <servlet-name>AppointmentServlet</servlet-name>
        <servlet-class>com.doctorapp.controller.servlets.AppointmentServlet</servlet-class>
        <multipart-config>
            <location>/tmp</location>
            <max-file-size>20848820</max-file-size>
            <max-request-size>418018841</max-request-size>
            <file-size-threshold>1048576</file-size-threshold>
        </multipart-config>
    </servlet>
    <servlet-mapping>
        <servlet-name>AppointmentServlet</servlet-name>
        <url-pattern>/appointment/*</url-pattern>
    </servlet-mapping>

    <!-- Doctor Profile Servlet Multipart Configuration -->
    <servlet>
        <servlet-name>DoctorProfileServlet</servlet-name>
        <servlet-class>com.doctorapp.controller.doctor.DoctorProfileServlet</servlet-class>
        <multipart-config>
            <!-- Using relative path instead of absolute path -->
            <file-size-threshold>1048576</file-size-threshold>
            <max-file-size>5242880</max-file-size>
            <max-request-size>10485760</max-request-size>
        </multipart-config>
    </servlet>
    <servlet-mapping>
        <servlet-name>DoctorProfileServlet</servlet-name>
        <url-pattern>/doctor/profile</url-pattern>
        <url-pattern>/doctor/edit-profile</url-pattern>
    </servlet-mapping>

    <!-- Image Servlet Configuration -->
    <servlet>
        <servlet-name>ImageServlet</servlet-name>
        <servlet-class>com.doctorapp.controller.servlets.ImageServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>ImageServlet</servlet-name>
        <url-pattern>/image/*</url-pattern>
    </servlet-mapping>

    <!--
        Medical Record View/Edit Servlet is configured via @WebServlet annotation
        in com.doctorapp.controller.doctor.DoctorViewEditMedicalRecordServlet
    -->

    <!-- Doctor Appointment Details Page Servlet -->
    <servlet>
        <servlet-name>DoctorAppointmentDetailsPageServlet</servlet-name>
        <servlet-class>com.doctorapp.controller.doctor.DoctorAppointmentDetailsPageServlet</servlet-class>
    </servlet>
    <servlet-mapping>
        <servlet-name>DoctorAppointmentDetailsPageServlet</servlet-name>
        <url-pattern>/doctor/appointment-details</url-pattern>
    </servlet-mapping>

</web-app>
