/* Doctor Profile Dashboard CSS */
:root {
    --primary-color: #3498db;
    --primary-dark: #2980b9;
    --primary-light: #e3f2fd;
    --secondary-color: #2ecc71;
    --secondary-dark: #27ae60;
    --secondary-light: #e8f5e9;
    --danger-color: #e74c3c;
    --danger-light: #ffebee;
    --warning-color: #f39c12;
    --warning-light: #fff8e1;
    --info-color: #3498db;
    --info-light: #e3f2fd;
    --success-color: #2ecc71;
    --success-light: #e8f5e9;
    --dark-color: #2c3e50;
    --light-color: #f5f7fa;
    --border-color: #e0e0e0;
    --text-dark: #333333;
    --text-light: #777777;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    font-size: 14px;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: #f5f7fa;
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: all 0.3s ease;
}

a:hover {
    color: var(--primary-dark);
}

/* Layout */
.dashboard-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar */
.sidebar {
    width: 240px;
    background-color: var(--dark-color);
    color: white;
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    overflow-y: auto;
    z-index: 1000;
    transition: all 0.3s ease;
}

.sidebar-header {
    padding: 20px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-header img {
    width: 30px;
    height: 30px;
    margin-right: 10px;
}

.sidebar-header h2 {
    font-size: 18px;
    font-weight: 600;
    color: white;
    margin: 0;
}

.sidebar-menu {
    padding: 20px 0;
}

.sidebar-menu ul {
    list-style: none;
}

.sidebar-menu li {
    margin-bottom: 5px;
}

.sidebar-menu li a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: rgba(255, 255, 255, 0.7);
    transition: all 0.3s ease;
}

.sidebar-menu li a:hover {
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
}

.sidebar-menu li.active a {
    background-color: var(--primary-color);
    color: white;
}

.sidebar-menu li a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

.sidebar-menu li.logout {
    margin-top: 30px;
}

.sidebar-menu li.logout a {
    color: var(--danger-color);
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: 180px;
    padding: 20px;
    max-width: calc(100% - 180px);
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Dashboard Content */
.dashboard-content {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
}

/* Profile Overview */
.profile-overview {
    background-color: white;
    border-radius: 8px;
    box-shadow: var(--shadow);
    padding: 20px;
    margin-bottom: 20px;
    display: flex;
    align-items: flex-start;
}

.doctor-profile {
    display: flex;
    align-items: center;
    flex: 1;
}

.profile-image {
    width: 80px;
    height: 80px;
    border-radius: 8px;
    object-fit: cover;
    margin-right: 20px;
}

.profile-info h2 {
    font-size: 20px;
    font-weight: 600;
    margin-bottom: 5px;
    color: var(--dark-color);
}

.profile-info p {
    color: var(--text-light);
    margin-bottom: 5px;
}

.profile-actions {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 4px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    font-size: 14px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-outline {
    background-color: transparent;
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
}

.btn-outline:hover {
    background-color: var(--primary-light);
}

.btn-danger {
    background-color: var(--danger-color);
    color: white;
}

.btn-danger:hover {
    background-color: #c0392b;
}

/* Appointment Management */
.appointment-management {
    background-color: white;
    border-radius: 8px;
    box-shadow: var(--shadow);
    margin-bottom: 20px;
    overflow: hidden;
}

.section-header {
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.section-header h2 {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
}

.tab {
    padding: 12px 20px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
    border-bottom: 2px solid transparent;
}

.tab.active {
    color: var(--primary-color);
    border-bottom-color: var(--primary-color);
}

/* Tables */
.table-container {
    padding: 20px;
    overflow-x: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

table th, table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid var(--border-color);
}

table th {
    font-weight: 600;
    color: var(--text-dark);
    background-color: var(--light-color);
}

table tr:last-child td {
    border-bottom: none;
}

/* Status badges */
.status {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
}

.status.pending {
    background-color: var(--warning-light);
    color: var(--warning-color);
}

.status.approved, .status.confirmed {
    background-color: var(--success-light);
    color: var(--success-color);
}

.status.cancelled {
    background-color: var(--danger-light);
    color: var(--danger-color);
}

/* Action buttons */
.action-btn {
    width: 30px;
    height: 30px;
    border-radius: 4px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-right: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn.view {
    background-color: var(--info-color);
}

.action-btn.edit {
    background-color: var(--warning-color);
}

/* Stats Cards */
.stats-container {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 20px;
}

.stat-card {
    background-color: white;
    border-radius: 8px;
    box-shadow: var(--shadow);
    padding: 20px;
    display: flex;
    align-items: center;
}

.stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 20px;
    color: white;
}

.stat-icon.primary {
    background-color: var(--primary-color);
}

.stat-icon.success {
    background-color: var(--success-color);
}

.stat-icon.warning {
    background-color: var(--warning-color);
}

.stat-icon.info {
    background-color: var(--info-color);
}

.stat-content {
    flex: 1;
}

.stat-value {
    font-size: 24px;
    font-weight: 700;
    margin-bottom: 5px;
}

.stat-label {
    color: var(--text-light);
    font-size: 14px;
}

/* Responsive */
@media (max-width: 992px) {
    .stats-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 70px;
    }

    .sidebar-header h2,
    .sidebar-menu li a span {
        display: none;
    }

    .main-content {
        margin-left: 70px;
    }

    .profile-overview {
        flex-direction: column;
    }

    .doctor-profile {
        margin-bottom: 20px;
    }
}

@media (max-width: 576px) {
    .stats-container {
        grid-template-columns: 1fr;
    }

    .profile-actions {
        flex-direction: column;
    }
}
