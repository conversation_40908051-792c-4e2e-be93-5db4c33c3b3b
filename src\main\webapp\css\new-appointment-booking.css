/* New Appointment Booking Styles */

/* Green header */
.green-header {
    background-color: #4CAF50;
    color: white;
    padding: 20px;
    margin-bottom: 30px;
}

/* Patient info in header */
.patient-info {
    display: flex;
    align-items: center;
}

.patient-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #4CAF50;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: 600;
    margin-right: 15px;
    border: 3px solid white;
}

/* Appointment form */
.appointment-form {
    padding: 0 20px;
}

/* Form group */
.form-group {
    margin-bottom: 25px;
}

.form-group label {
    display: block;
    margin-bottom: 10px;
    font-weight: 500;
    color: #2c3e50;
    font-size: 1.1rem;
}

/* Date input */
input[type="date"] {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
    color: #333;
    background-color: #fff;
}

input[type="date"]:focus {
    border-color: #4CAF50;
    outline: none;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

/* Time slots */
.time-slots {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 20px;
}

.time-slot {
    position: relative;
}

.time-slot input[type="radio"] {
    position: absolute;
    opacity: 0;
    cursor: pointer;
}

.time-slot label {
    display: block;
    padding: 14px 10px;
    text-align: center;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-weight: 500;
    font-size: 1rem;
    color: #555;
    min-width: 100px;
}

.time-slot input[type="radio"]:checked + label {
    background-color: #4CAF50;
    color: white;
    border-color: #4CAF50;
    box-shadow: 0 3px 8px rgba(76, 175, 80, 0.3);
    transform: translateY(-2px);
}

.time-slot label:hover {
    background-color: #e9ecef;
    border-color: #ced4da;
    transform: translateY(-1px);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.time-slot input[type="radio"]:checked + label:hover {
    background-color: #43A047;
}

/* Symptoms textarea */
.symptoms-textarea {
    width: 100%;
    min-height: 150px;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 15px;
    margin-top: 10px;
    resize: vertical;
    font-family: 'Poppins', sans-serif;
    font-size: 0.95rem;
}

.symptoms-textarea:focus {
    border-color: #4CAF50;
    outline: none;
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

/* Confirm booking button */
.confirm-booking-btn {
    background-color: #4CAF50;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 5px;
    cursor: pointer;
    font-weight: 500;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: auto;
    margin-top: 20px;
    transition: all 0.3s;
}

.confirm-booking-btn:hover {
    background-color: #43A047;
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.4);
    transform: translateY(-2px);
}

.confirm-booking-btn i {
    margin-right: 8px;
}

/* Responsive styles */
@media (max-width: 768px) {
    .time-slots {
        grid-template-columns: repeat(auto-fill, minmax(90px, 1fr));
        gap: 10px;
    }
    
    .confirm-booking-btn {
        width: 100%;
    }
    
    .green-header {
        padding: 15px;
    }
    
    .patient-avatar {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }
}

@media (max-width: 480px) {
    .time-slots {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;
    }
    
    .time-slot label {
        padding: 10px 8px;
        font-size: 0.9rem;
        min-width: auto;
    }
}
