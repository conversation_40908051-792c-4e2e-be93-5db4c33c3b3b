-- Consolidated Database Schema for Doctor Appointment System
-- This file combines all SQL scripts into a single comprehensive schema

-- Create database if it doesn't exist
CREATE DATABASE IF NOT EXISTS doctor_appointment;
USE doctor_appointment;

-- Tables will be created only if they don't exist
-- This preserves existing data in the database

-- Create users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    first_name VARCHAR(50) NOT NULL,
    last_name VA<PERSON>HAR(50) NOT NULL,
    phone VARCHAR(20),
    role ENUM('ADMIN', 'DOCTOR', 'PATIENT') NOT NULL,
    date_of_birth DATE,
    gender ENUM('Male', 'Female', 'Other'),
    address TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create doctor_registration_requests table
CREATE TABLE IF NOT EXISTS doctor_registration_requests (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    phone VARCHAR(20),
    first_name VARCHAR(50),
    last_name VARCHAR(50),
    specialization VARCHAR(100) NOT NULL,
    qualification VARCHAR(255) NOT NULL,
    experience VARCHAR(50),
    address TEXT,
    status ENUM('PENDING', 'APPROVED', 'REJECTED') DEFAULT 'PENDING',
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Create doctors table
CREATE TABLE IF NOT EXISTS doctors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    name VARCHAR(100),
    specialization VARCHAR(100) NOT NULL,
    qualification VARCHAR(255) NOT NULL,
    experience INT DEFAULT 0,
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    consultation_fee DECIMAL(10, 2) DEFAULT 0.00,
    available_days VARCHAR(100) DEFAULT 'Monday-Friday',
    available_time VARCHAR(100) DEFAULT '09:00 AM - 05:00 PM',
    profile_image VARCHAR(255),
    image_url VARCHAR(255),
    bio TEXT,
    license_number VARCHAR(100) DEFAULT NULL,
    rating DECIMAL(3, 1) DEFAULT 0.0,
    patient_count INT DEFAULT 0,
    success_rate INT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create patients table
CREATE TABLE IF NOT EXISTS patients (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    blood_group VARCHAR(10),
    allergies TEXT,
    medical_history TEXT,
    profile_image VARCHAR(255) DEFAULT '/assets/images/patients/default.jpg',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- Create doctor_ratings table
CREATE TABLE IF NOT EXISTS doctor_ratings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    doctor_id INT NOT NULL,
    patient_id INT NOT NULL,
    rating DECIMAL(3, 1) NOT NULL,
    review TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (doctor_id) REFERENCES doctors(id) ON DELETE CASCADE,
    FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE
);

-- Create doctor_schedules table
CREATE TABLE IF NOT EXISTS doctor_schedules (
    id INT AUTO_INCREMENT PRIMARY KEY,
    doctor_id INT NOT NULL,
    day_of_week ENUM('Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday') NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    break_start_time TIME,
    break_end_time TIME,
    max_appointments INT DEFAULT 20,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (doctor_id) REFERENCES doctors(id) ON DELETE CASCADE
);

-- Create appointments table
CREATE TABLE IF NOT EXISTS appointments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    patient_id INT NOT NULL,
    doctor_id INT NOT NULL,
    appointment_date DATE NOT NULL,
    appointment_time VARCHAR(20) NOT NULL,
    status ENUM('PENDING', 'CONFIRMED', 'CANCELLED', 'COMPLETED') DEFAULT 'PENDING',
    reason VARCHAR(255),
    symptoms TEXT,
    notes TEXT,
    prescription TEXT,
    medical_report TEXT,
    fee DECIMAL(10, 2) DEFAULT 0.00,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
    FOREIGN KEY (doctor_id) REFERENCES doctors(id) ON DELETE CASCADE
);

-- Create medical_records table
CREATE TABLE IF NOT EXISTS medical_records (
    id INT AUTO_INCREMENT PRIMARY KEY,
    patient_id INT NOT NULL,
    doctor_id INT NOT NULL,
    record_date DATE NOT NULL,
    diagnosis TEXT NOT NULL,
    treatment TEXT,
    prescription TEXT,
    notes TEXT,
    attachments VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
    FOREIGN KEY (doctor_id) REFERENCES doctors(id) ON DELETE CASCADE
);

-- Create prescriptions table
CREATE TABLE IF NOT EXISTS prescriptions (
    id INT AUTO_INCREMENT PRIMARY KEY,
    medical_record_id INT,
    appointment_id INT,
    patient_id INT NOT NULL,
    doctor_id INT NOT NULL,
    prescription_date DATE NOT NULL,
    medication_name VARCHAR(255) NOT NULL,
    dosage VARCHAR(100) NOT NULL,
    frequency VARCHAR(100) NOT NULL,
    duration VARCHAR(100) NOT NULL,
    instructions TEXT,
    status ENUM('ACTIVE', 'COMPLETED', 'CANCELLED') DEFAULT 'ACTIVE',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (medical_record_id) REFERENCES medical_records(id) ON DELETE SET NULL,
    FOREIGN KEY (appointment_id) REFERENCES appointments(id) ON DELETE SET NULL,
    FOREIGN KEY (patient_id) REFERENCES patients(id) ON DELETE CASCADE,
    FOREIGN KEY (doctor_id) REFERENCES doctors(id) ON DELETE CASCADE
);

-- Create announcements table
CREATE TABLE IF NOT EXISTS announcements (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    start_date DATE,
    end_date DATE,
    created_by INT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
);

-- Insert default admin user (password: admin123)
INSERT IGNORE INTO users (id, username, email, password, first_name, last_name, role)
VALUES (1, 'admin', '<EMAIL>', '$bcrypt$YWRtaW4xMjM=$YWRtaW4xMjM=', 'System', 'Administrator', 'ADMIN');

-- Insert sample doctors
INSERT IGNORE INTO users (id, username, email, password, first_name, last_name, phone, role, gender, address)
VALUES
(2, 'dr_smith', '<EMAIL>', '$bcrypt$ZG9jdG9yMTIz$ZG9jdG9yMTIz', 'John', 'Smith', '+**********', 'DOCTOR', 'Male', '123 Medical Center, City'),
(3, 'dr_johnson', '<EMAIL>', '$bcrypt$ZG9jdG9yMTIz$ZG9jdG9yMTIz', 'Sarah', 'Johnson', '+**********', 'DOCTOR', 'Female', '456 Health Plaza, City'),
(4, 'dr_williams', '<EMAIL>', '$bcrypt$ZG9jdG9yMTIz$ZG9jdG9yMTIz', 'Michael', 'Williams', '+**********', 'DOCTOR', 'Male', '789 Care Avenue, City');

INSERT IGNORE INTO doctors (id, user_id, name, specialization, qualification, experience, phone, email, consultation_fee, bio, license_number, rating, patient_count)
VALUES
(1, 2, 'Dr. John Smith', 'Cardiology', 'MD, FACC', 15, '+**********', '<EMAIL>', 150.00, 'Experienced cardiologist with 15 years of practice.', 'MD12345', 4.8, 250),
(2, 3, 'Dr. Sarah Johnson', 'Dermatology', 'MD, FAAD', 10, '+**********', '<EMAIL>', 120.00, 'Specialist in skin care and cosmetic dermatology.', 'MD12346', 4.7, 180),
(3, 4, 'Dr. Michael Williams', 'Orthopedics', 'MD, FAAOS', 12, '+**********', '<EMAIL>', 180.00, 'Expert in bone and joint treatments.', 'MD12347', 4.9, 320);

-- Insert sample patients
INSERT IGNORE INTO users (id, username, email, password, first_name, last_name, phone, role, gender, date_of_birth, address)
VALUES
(5, 'patient1', '<EMAIL>', '$bcrypt$cGF0aWVudDEyMw==$cGF0aWVudDEyMw==', 'Alice', 'Brown', '+**********', 'PATIENT', 'Female', '1990-05-15', '321 Patient Street, City'),
(6, 'patient2', '<EMAIL>', '$bcrypt$cGF0aWVudDEyMw==$cGF0aWVudDEyMw==', 'Bob', 'Davis', '+**********', 'PATIENT', 'Male', '1985-08-22', '654 Health Road, City');

INSERT IGNORE INTO patients (id, user_id, blood_group, allergies, medical_history)
VALUES
(1, 5, 'A+', 'Penicillin', 'No major medical history'),
(2, 6, 'O-', 'None', 'Diabetes Type 2');

-- Insert sample appointments
INSERT IGNORE INTO appointments (id, patient_id, doctor_id, appointment_date, appointment_time, status, reason, fee)
VALUES
(1, 1, 1, '2024-01-15', '10:00 AM', 'CONFIRMED', 'Regular checkup', 150.00),
(2, 2, 2, '2024-01-16', '02:00 PM', 'PENDING', 'Skin consultation', 120.00),
(3, 1, 3, '2024-01-17', '11:00 AM', 'COMPLETED', 'Knee pain', 180.00);

