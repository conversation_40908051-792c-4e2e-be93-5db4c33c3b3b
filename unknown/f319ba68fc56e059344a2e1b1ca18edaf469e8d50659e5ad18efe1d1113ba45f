/* Doctor Dashboard CSS */
:root {
    --primary-color: #1976D2;
    --primary-light: #BBDEFB;
    --primary-dark: #0D47A1;
    --secondary-color: #4CAF50;
    --secondary-light: #C8E6C9;
    --secondary-dark: #2E7D32;
    --warning-color: #FF9800;
    --danger-color: #F44336;
    --text-dark: #333333;
    --text-light: #757575;
    --text-white: #FFFFFF;
    --bg-light: #F5F7FA;
    --bg-white: #FFFFFF;
    --border-color: #E0E0E0;
    --shadow-color: rgba(0, 0, 0, 0.1);
    --transition-speed: 0.3s;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Roboto', 'Segoe UI', sans-serif;
    font-size: 16px;
    line-height: 1.5;
    color: var(--text-dark);
    background-color: var(--bg-light);
}

.dashboard-body {
    display: flex;
    min-height: 100vh;
}

.dashboard-container {
    display: flex;
    width: 100%;
    min-height: 100vh;
}

/* Sidebar Styles */
.sidebar {
    width: 280px;
    background-color: var(--bg-white);
    border-right: 1px solid var(--border-color);
    display: flex;
    flex-direction: column;
    transition: width var(--transition-speed);
    box-shadow: 2px 0 5px var(--shadow-color);
    z-index: 100;
}

.sidebar.collapsed {
    width: 80px;
}

.sidebar-header {
    padding: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
}

.doctor-profile-small {
    position: relative;
    margin-bottom: 10px;
}

.profile-pic-small {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    border: 3px solid var(--primary-light);
}

.status-indicator {
    position: absolute;
    bottom: 5px;
    right: 5px;
    width: 15px;
    height: 15px;
    border-radius: 50%;
    border: 2px solid var(--bg-white);
}

.status-indicator.active {
    background-color: var(--secondary-color);
}

.status-indicator.inactive {
    background-color: var(--text-light);
}

.sidebar-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin: 10px 0;
    text-align: center;
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    color: var(--text-dark);
    font-size: 1.2rem;
    cursor: pointer;
}

.sidebar-nav {
    flex: 1;
    padding: 20px 0;
    overflow-y: auto;
}

.sidebar-nav ul {
    list-style: none;
}

.sidebar-nav li {
    margin-bottom: 5px;
}

.sidebar-nav a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: var(--text-dark);
    text-decoration: none;
    transition: all var(--transition-speed);
    border-left: 3px solid transparent;
}

.sidebar-nav a:hover {
    background-color: var(--primary-light);
    color: var(--primary-dark);
}

.sidebar-nav li.active a {
    background-color: var(--primary-light);
    color: var(--primary-dark);
    border-left-color: var(--primary-color);
}

.sidebar-nav a i {
    margin-right: 15px;
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

.sidebar.collapsed .sidebar-nav a span {
    display: none;
}

.sidebar-footer {
    padding: 20px;
    border-top: 1px solid var(--border-color);
}

.status-toggle {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.status-toggle span {
    margin-right: 10px;
}

.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
    margin: 0 10px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--text-light);
    transition: .4s;
}

.slider:before {
    position: absolute;
    content: "";
    height: 16px;
    width: 16px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

input:checked + .slider {
    background-color: var(--secondary-color);
}

input:focus + .slider {
    box-shadow: 0 0 1px var(--secondary-color);
}

input:checked + .slider:before {
    transform: translateX(26px);
}

.slider.round {
    border-radius: 34px;
}

.slider.round:before {
    border-radius: 50%;
}

.logout-btn {
    display: flex;
    align-items: center;
    padding: 10px;
    color: var(--danger-color);
    text-decoration: none;
    border-radius: 5px;
    transition: all var(--transition-speed);
}

.logout-btn:hover {
    background-color: rgba(244, 67, 54, 0.1);
}

.logout-btn i {
    margin-right: 10px;
}

/* Main Content Styles */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow-x: hidden;
}

/* Top Header Styles */
.top-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 30px;
    background-color: var(--bg-white);
    border-bottom: 1px solid var(--border-color);
    box-shadow: 0 2px 5px var(--shadow-color);
}

.current-date {
    display: flex;
    align-items: center;
    font-size: 1rem;
    color: var(--text-light);
}

.current-date i {
    margin-right: 10px;
    color: var(--primary-color);
}

.header-actions {
    display: flex;
    align-items: center;
}

.search-container {
    position: relative;
    margin-right: 20px;
}

.search-input {
    padding: 8px 15px 8px 40px;
    border: 1px solid var(--border-color);
    border-radius: 20px;
    width: 250px;
    font-size: 0.9rem;
    transition: all var(--transition-speed);
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px var(--primary-light);
    width: 300px;
}

.search-btn {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
}

.notifications {
    position: relative;
    margin-right: 20px;
}

.notification-btn {
    background: none;
    border: none;
    color: var(--text-dark);
    font-size: 1.2rem;
    cursor: pointer;
    position: relative;
}

.notification-badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--danger-color);
    color: var(--text-white);
    font-size: 0.7rem;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.notification-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    width: 350px;
    background-color: var(--bg-white);
    border-radius: 5px;
    box-shadow: 0 5px 15px var(--shadow-color);
    z-index: 100;
    display: none;
}

.notifications:hover .notification-dropdown {
    display: block;
}

.notification-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
}

.notification-header h3 {
    font-size: 1rem;
    font-weight: 600;
}

.notification-header a {
    color: var(--primary-color);
    font-size: 0.9rem;
    text-decoration: none;
}

.notification-list {
    max-height: 300px;
    overflow-y: auto;
    list-style: none;
}

.notification-list li {
    display: flex;
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    transition: background-color var(--transition-speed);
}

.notification-list li:hover {
    background-color: var(--bg-light);
}

.notification-list li.unread {
    background-color: rgba(25, 118, 210, 0.05);
}

.notification-icon {
    margin-right: 15px;
    color: var(--primary-color);
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

.notification-content p {
    margin: 0;
    font-size: 0.9rem;
}

.notification-time {
    display: block;
    font-size: 0.8rem;
    color: var(--text-light);
    margin-top: 5px;
}

.notification-footer {
    padding: 15px;
    text-align: center;
    border-top: 1px solid var(--border-color);
}

.notification-footer a {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
}

.account-dropdown {
    position: relative;
}

.account-btn {
    display: flex;
    align-items: center;
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px 10px;
    border-radius: 20px;
    transition: background-color var(--transition-speed);
}

.account-btn:hover {
    background-color: var(--bg-light);
}

.profile-pic-tiny {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    object-fit: cover;
    margin-right: 10px;
}

.account-btn span {
    margin-right: 10px;
    font-weight: 500;
}

.account-dropdown-menu {
    position: absolute;
    top: 100%;
    right: 0;
    width: 200px;
    background-color: var(--bg-white);
    border-radius: 5px;
    box-shadow: 0 5px 15px var(--shadow-color);
    z-index: 100;
    display: none;
}

.account-dropdown:hover .account-dropdown-menu {
    display: block;
}

.account-dropdown-menu a {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    color: var(--text-dark);
    text-decoration: none;
    transition: background-color var(--transition-speed);
}

.account-dropdown-menu a:hover {
    background-color: var(--bg-light);
}

.account-dropdown-menu a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
}

/* Dashboard Sections Styles */
.dashboard-sections {
    flex: 1;
    padding: 30px;
    overflow-y: auto;
}

.dashboard-section {
    display: none;
}

.dashboard-section.active {
    display: block;
}

.section-title {
    font-size: 1.8rem;
    font-weight: 600;
    margin-bottom: 25px;
    color: var(--text-dark);
}

/* Summary Cards Styles */
.summary-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background-color: var(--bg-white);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px var(--shadow-color);
    display: flex;
    align-items: center;
}

.summary-card.warning .card-icon {
    background-color: rgba(255, 152, 0, 0.1);
    color: var(--warning-color);
}

.card-icon {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    background-color: rgba(25, 118, 210, 0.1);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-right: 15px;
}

.card-content {
    flex: 1;
}

.card-content h3 {
    font-size: 0.9rem;
    font-weight: 500;
    color: var(--text-light);
    margin-bottom: 5px;
}

.card-value {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-dark);
    display: flex;
    align-items: center;
}

.trend {
    font-size: 0.8rem;
    margin-left: 10px;
    display: flex;
    align-items: center;
}

.trend.up {
    color: var(--secondary-color);
}

.trend.down {
    color: var(--danger-color);
}

.trend i {
    margin-right: 3px;
}

.progress-bar {
    height: 6px;
    background-color: var(--bg-light);
    border-radius: 3px;
    margin-top: 10px;
    overflow: hidden;
}

.progress {
    height: 100%;
    background-color: var(--primary-color);
    border-radius: 3px;
}

.mini-graph {
    margin-top: 10px;
    height: 20px;
}

/* Today's Schedule Styles */
.today-schedule {
    background-color: var(--bg-white);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 10px var(--shadow-color);
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.section-header h3 {
    font-size: 1.2rem;
    font-weight: 600;
}

.view-all {
    color: var(--primary-color);
    text-decoration: none;
    font-size: 0.9rem;
    display: flex;
    align-items: center;
}

.view-all i {
    margin-left: 5px;
}

.timeline {
    position: relative;
}

.timeline:before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 70px;
    width: 2px;
    background-color: var(--border-color);
}

.timeline-item {
    display: flex;
    margin-bottom: 20px;
    position: relative;
}

.timeline-time {
    width: 70px;
    text-align: right;
    padding-right: 20px;
    font-weight: 600;
    color: var(--text-dark);
}

.timeline-content {
    flex: 1;
    background-color: var(--bg-light);
    border-radius: 5px;
    padding: 15px;
    margin-left: 20px;
    position: relative;
}

.timeline-content:before {
    content: '';
    position: absolute;
    top: 50%;
    left: -26px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--primary-color);
    transform: translateY(-50%);
    border: 2px solid var(--bg-white);
}

.timeline-item.CONFIRMED .timeline-content:before {
    background-color: var(--secondary-color);
}

.timeline-item.PENDING .timeline-content:before {
    background-color: var(--warning-color);
}

.timeline-item.CANCELLED .timeline-content:before {
    background-color: var(--danger-color);
}

.appointment-type {
    font-size: 0.8rem;
    color: var(--text-light);
    margin-bottom: 5px;
}

.patient-name {
    font-weight: 600;
    margin-bottom: 10px;
}

.appointment-actions {
    display: flex;
    gap: 10px;
}

.appointment-actions button {
    background: none;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-speed);
}

.btn-view {
    background-color: rgba(25, 118, 210, 0.1);
    color: var(--primary-color);
}

.btn-view:hover {
    background-color: var(--primary-color);
    color: var(--text-white);
}

.btn-reschedule {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--secondary-color);
}

.btn-reschedule:hover {
    background-color: var(--secondary-color);
    color: var(--text-white);
}

.btn-cancel {
    background-color: rgba(244, 67, 54, 0.1);
    color: var(--danger-color);
}

.btn-cancel:hover {
    background-color: var(--danger-color);
    color: var(--text-white);
}

.empty-schedule {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 0;
    color: var(--text-light);
}

.empty-schedule i {
    font-size: 3rem;
    margin-bottom: 15px;
    color: var(--border-color);
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .summary-cards {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 992px) {
    .sidebar {
        width: 80px;
    }

    .sidebar-header h3,
    .sidebar-nav a span,
    .status-toggle span,
    #status-text,
    .logout-btn span {
        display: none;
    }

    .sidebar-nav a i {
        margin-right: 0;
    }

    .sidebar-toggle {
        display: block;
    }

    .main-content {
        margin-left: 80px;
    }

    .search-input {
        width: 200px;
    }

    .search-input:focus {
        width: 250px;
    }
}

@media (max-width: 768px) {
    .dashboard-container {
        flex-direction: column;
    }

    .sidebar {
        width: 100%;
        height: auto;
        position: fixed;
        bottom: 0;
        left: 0;
        flex-direction: row;
        justify-content: space-around;
        padding: 10px;
        z-index: 1000;
    }

    .sidebar-header,
    .sidebar-footer,
    .doctor-profile-small {
        display: none;
    }

    .sidebar-nav {
        padding: 0;
        overflow-x: auto;
        width: 100%;
    }

    .sidebar-nav ul {
        display: flex;
    }

    .sidebar-nav li {
        margin: 0 5px;
    }

    .sidebar-nav a {
        padding: 10px;
        flex-direction: column;
        border-left: none;
        border-bottom: 3px solid transparent;
        text-align: center;
    }

    .sidebar-nav li.active a {
        border-left-color: transparent;
        border-bottom-color: var(--primary-color);
    }

    .sidebar-nav a i {
        margin-right: 0;
        margin-bottom: 5px;
    }

    .sidebar-nav a span {
        display: block;
        font-size: 0.7rem;
    }

    .main-content {
        margin-left: 0;
        margin-bottom: 70px;
    }

    .top-header {
        padding: 10px 15px;
    }

    .current-date {
        display: none;
    }

    .search-container {
        margin-right: 10px;
    }

    .search-input {
        width: 150px;
        padding: 6px 10px 6px 30px;
    }

    .search-input:focus {
        width: 180px;
    }

    .account-btn span {
        display: none;
    }

    .dashboard-sections {
        padding: 15px;
    }

    .summary-cards {
        grid-template-columns: 1fr;
    }
}

/* Modal Styles */
.modal-content {
    border-radius: 10px;
    overflow: hidden;
}

.modal-header {
    background-color: var(--primary-color);
    color: var(--text-white);
    padding: 15px 20px;
}

.modal-title {
    font-weight: 600;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
}

/* Animation Styles */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

/* Accessibility Styles */
.visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    margin: -1px;
    padding: 0;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    border: 0;
}

/* Focus styles for keyboard navigation */
a:focus, button:focus, input:focus, select:focus, textarea:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* High contrast mode */
@media (prefers-contrast: high) {
    :root {
        --primary-color: #0000FF;
        --primary-light: #CCCCFF;
        --primary-dark: #000088;
        --secondary-color: #008800;
        --secondary-light: #CCFFCC;
        --secondary-dark: #006600;
        --warning-color: #FF8800;
        --danger-color: #FF0000;
        --text-dark: #000000;
        --text-light: #444444;
        --text-white: #FFFFFF;
        --bg-light: #FFFFFF;
        --bg-white: #FFFFFF;
        --border-color: #000000;
        --shadow-color: rgba(0, 0, 0, 0.5);
    }

    .sidebar-nav a, .account-dropdown-menu a {
        text-decoration: underline;
    }

    .status-indicator.active {
        background-color: #00FF00;
    }

    .status-indicator.inactive {
        background-color: #888888;
    }
}

/* Dark mode */
@media (prefers-color-scheme: dark) {
    :root {
        --primary-color: #90CAF9;
        --primary-light: #1E3A5F;
        --primary-dark: #BBDEFB;
        --secondary-color: #A5D6A7;
        --secondary-light: #1E3B1E;
        --secondary-dark: #C8E6C9;
        --warning-color: #FFB74D;
        --danger-color: #EF9A9A;
        --text-dark: #E0E0E0;
        --text-light: #9E9E9E;
        --text-white: #FFFFFF;
        --bg-light: #121212;
        --bg-white: #1E1E1E;
        --border-color: #333333;
        --shadow-color: rgba(0, 0, 0, 0.5);
    }
}
