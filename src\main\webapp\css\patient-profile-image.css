/*
 * Patient Profile Image Styles
 * This file contains unified styling for patient profile images across the application
 */

/* Common profile image container styles */
.profile-image,
.patient-avatar,
.user-avatar,
.doctor-image,
.profile-avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    overflow: hidden;
    background-color: #f5f5f5;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 3px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    margin: 0 auto;
    position: relative;
}

/* Profile image in sidebar - smaller size */
.sidebar .profile-image,
.sidebar .patient-avatar,
.sidebar .user-avatar {
    width: 60px;
    height: 60px;
    margin-bottom: 15px;
}

/* Profile image in header/dashboard - larger size */
.profile-header .profile-image,
.patient-info .patient-avatar,
.dashboard-header .user-avatar {
    width: 120px;
    height: 120px;
    margin-right: 20px;
    margin-bottom: 0;
}

/* Profile image in appointment cards - smaller size */
.appointment-card .doctor-image,
.doctor-info .doctor-image,
.appointment-body .patient-avatar {
    width: 60px;
    height: 60px;
    margin-right: 15px;
    border-width: 2px;
}

/* Image styling */
.profile-image img,
.patient-avatar img,
.user-avatar img,
.doctor-image img,
.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
}

/* Hover effect */
.profile-image:hover img,
.patient-avatar:hover img,
.user-avatar:hover img {
    transform: scale(1.05);
}

/* Fallback for missing images - initials display */
.profile-initials,
.user-initials {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #4CAF50;
    color: white;
    font-size: 2rem;
    font-weight: 600;
    text-transform: uppercase;
}

/* Smaller font size for initials in smaller containers */
.appointment-card .profile-initials,
.doctor-info .profile-initials,
.appointment-body .profile-initials,
.appointment-card .user-initials,
.doctor-info .user-initials,
.appointment-body .user-initials {
    font-size: 1.2rem;
}

/* Error handling for images */
.profile-image img[src=""],
.profile-image img:not([src]),
.patient-avatar img[src=""],
.patient-avatar img:not([src]),
.user-avatar img[src=""],
.user-avatar img:not([src]),
.doctor-image img[src=""],
.doctor-image img:not([src]),
.profile-avatar img[src=""],
.profile-avatar img:not([src]) {
    display: none;
}

/* Image loading optimization */
.profile-image img,
.patient-avatar img,
.user-avatar img,
.doctor-image img,
.profile-avatar img {
    will-change: transform;
    backface-visibility: hidden;
}

/* Responsive styles */
@media (max-width: 768px) {
    .profile-header .profile-image,
    .patient-info .patient-avatar,
    .dashboard-header .user-avatar {
        width: 100px;
        height: 100px;
        margin-right: 0;
        margin-bottom: 15px;
    }

    .profile-initials,
    .user-initials {
        font-size: 1.8rem;
    }
}

@media (max-width: 480px) {
    .profile-header .profile-image,
    .patient-info .patient-avatar,
    .dashboard-header .user-avatar {
        width: 80px;
        height: 80px;
    }

    .appointment-card .doctor-image,
    .doctor-info .doctor-image,
    .appointment-body .patient-avatar {
        width: 50px;
        height: 50px;
        margin-right: 10px;
    }

    .profile-initials,
    .user-initials {
        font-size: 1.5rem;
    }

    .appointment-card .profile-initials,
    .doctor-info .profile-initials,
    .appointment-body .profile-initials,
    .appointment-card .user-initials,
    .doctor-info .user-initials,
    .appointment-body .user-initials {
        font-size: 1rem;
    }
}
