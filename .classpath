<?xml version="1.0" encoding="UTF-8"?>
<classpath>
	<classpathentry kind="con" path="org.eclipse.jdt.launching.JRE_CONTAINER/org.eclipse.jdt.internal.debug.ui.launcher.StandardVMType/JavaSE-17">
		<attributes>
			<attribute name="module" value="true"/>
			<attribute name="maven.pomderived" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="src" output="target/classes" path="src/main/java">
		<attributes>
			<attribute name="optional" value="true"/>
			<attribute name="maven.pomderived" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="con" path="org.eclipse.jst.server.core.container/org.eclipse.jst.server.tomcat.runtimeTarget/apache-tomcat-11.0.0-M22">
		<attributes>
			<attribute name="owner.project.facets" value="jst.web"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="con" path="org.eclipse.jst.j2ee.internal.web.container"/>
	<classpathentry kind="con" path="org.eclipse.jst.j2ee.internal.module.container"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/h2-2.2.224.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/jakarta.annotation-api-2.0.0.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/jakarta.el-api-4.0.0.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/jakarta.json-api-2.0.1.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/jakarta.json.bind-api-2.0.0.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/jakarta.persistence-api-3.0.0.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/jakarta.security.enterprise-api-2.0.0.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/jakarta.servlet-api-5.0.0.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/jakarta.servlet.jsp-api-3.0.0.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/jakarta.servlet.jsp.jstl-2.0.0.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/jakarta.servlet.jsp.jstl-api-2.0.0.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/jakarta.transaction-api-2.0.0.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/jakarta.validation-api-3.0.0.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/jakarta.websocket-api-2.0.0.jar"/>
	<classpathentry kind="lib" path="src/main/webapp/WEB-INF/lib/mysql-connector-j-9.2.0.jar"/>
	<classpathentry excluding="**" kind="src" output="target/classes" path="src/main/resources">
		<attributes>
			<attribute name="maven.pomderived" value="true"/>
			<attribute name="optional" value="true"/>
		</attributes>
	</classpathentry>

	<classpathentry kind="con" path="org.eclipse.m2e.MAVEN2_CLASSPATH_CONTAINER">
		<attributes>
			<attribute name="maven.pomderived" value="true"/>
		</attributes>
	</classpathentry>
	<classpathentry kind="lib" path="C:/Users/<USER>/OneDrive - London Metropolitan University/Documents/mysql-connector-j-9.2.0.jar"/>
	<classpathentry kind="output" path="target/classes"/>
</classpath>
