/* Main Styles for Doctor Appointment System */

:root {
    --primary-color: #3498db; /* Changed to a modern blue */
    --primary-dark: #2980b9;
    --primary-light: #a8d4f5;
    --secondary-color: #2ecc71; /* Changed to a modern green */
    --secondary-dark: #27ae60;
    --secondary-light: #a9f5bc;
    --accent-color: #e74c3c; /* Added accent color (red) */
    --dark-color: #2c3e50; /* Darker shade */
    --light-color: #f9f9f9; /* Lighter shade */
    --danger-color: #e74c3c;
    --success-color: #2ecc71;
    --warning-color: #f39c12;
    --info-color: #3498db;
    --text-dark: #2c3e50;
    --text-light: #7f8c8d;
    --border-color:rgb(149, 198, 210);
    --shadow-color: rgba(0, 0, 0, 0.1);
    --gradient-primary: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    --transition: all 0.3s ease;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: var(--text-dark);
    background-color: var(--light-color);
    overflow-x: hidden; /* Prevent horizontal scroll */
}

a {
    text-decoration: none;
    color: var(--primary-color);
    transition: var(--transition);
}

ul {
    list-style: none;
}

img {
    max-width: 100%;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background-color: #fff;
    box-shadow: 0 2px 15px var(--shadow-color);
    position: sticky;
    top: 0;
    z-index: 100;
    transition: var(--transition);
}

.navbar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
}

.logo {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--dark-color);
}

.logo span {
    color: var(--secondary-color);
}

.nav-links {
    display: flex;
    align-items: center;
}

.nav-links li {
    margin-left: 20px;
}

.nav-links a {
    color: var(--text-dark);
    font-weight: 500;
    transition: color 0.3s;
}

.nav-links a:hover,
.nav-links a.active {
    color: var(--primary-color);
}

.login-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-light);
    color: var(--primary-dark);
    transition: all 0.3s;
}

.login-btn:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border-radius: 50px; /* Rounded buttons */
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition);
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background: var(--gradient-primary);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--secondary-dark));
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.btn-outline {
    border: 2px solid var(--primary-color);
    color: var(--primary-color);
    background-color: transparent;
    box-shadow: none;
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* Mobile Menu */
.mobile-menu {
    display: none;
    font-size: 1.5rem;
    cursor: pointer;
}

/* Hero Section */
.hero {
    background: linear-gradient(135deg, var(--primary-dark), var(--secondary-dark));
    padding: 120px 0 80px;
    color: white;
    position: relative;
    overflow: hidden;
}

.hero::before {
    content: '';
    position: absolute;
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    z-index: 0;
}

.hero::after {
    content: '';
    position: absolute;
    bottom: -100px;
    left: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    z-index: 0;
}

.hero-content {
    max-width: 600px;
    position: relative;
    z-index: 1;
    animation: fadeInUp 1s ease-out;
}

.hero-content h1 {
    font-size: 3.5rem;
    margin-bottom: 20px;
    color: white;
    font-weight: 700;
    line-height: 1.2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.hero-content h1 span {
    color: var(--secondary-light);
    position: relative;
    display: inline-block;
}

.hero-content h1 span::after {
    content: '';
    position: absolute;
    bottom: 5px;
    left: 0;
    width: 100%;
    height: 6px;
    background-color: var(--secondary-color);
    border-radius: 3px;
    z-index: -1;
    opacity: 0.5;
}

.hero-content p {
    font-size: 1.2rem;
    margin-bottom: 30px;
    color: rgba(255, 255, 255, 0.9);
    max-width: 90%;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.btn-group {
    display: flex;
    gap: 15px;
    margin-top: 30px;
}

.btn-group .btn {
    padding: 14px 28px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

/* Hero Features */
.hero-features {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-top: 80px;
    position: relative;
    z-index: 1;
}

.hero-feature {
    display: flex;
    align-items: center;
    background-color: white;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
    animation: fadeInUp 1s ease-out;
    animation-fill-mode: both;
}

.hero-feature:nth-child(1) {
    animation-delay: 0.2s;
}

.hero-feature:nth-child(2) {
    animation-delay: 0.4s;
}

.hero-feature:nth-child(3) {
    animation-delay: 0.6s;
}

.hero-feature:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.hero-feature-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: var(--gradient-primary);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    font-size: 1.5rem;
    color: white;
    box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.hero-feature-content h3 {
    font-size: 1.2rem;
    margin-bottom: 5px;
}

.hero-feature-content p {
    color: var(--text-light);
    font-size: 0.9rem;
}

/* Section Styles */
section {
    padding: 80px 0;
}

.section-title {
    text-align: center;
    margin-bottom: 50px;
}

.section-title h2 {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.section-title h2 span {
    color: var(--primary-color);
}

.section-title p {
    color: var(--text-light);
    max-width: 700px;
    margin: 0 auto;
}

/* Services Section */
.services {
    background-color: white;
    position: relative;
    overflow: hidden;
}

.services::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, var(--primary-light) 0%, rgba(255,255,255,0) 70%);
    opacity: 0.5;
    z-index: 0;
}

.services::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, var(--secondary-light) 0%, rgba(255,255,255,0) 70%);
    opacity: 0.5;
    z-index: 0;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    position: relative;
    z-index: 1;
}

.service-card {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
    background-color: white;
    border: 1px solid var(--border-color);
}

.service-card:hover {
    transform: translateY(-15px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-light);
}

.service-image {
    height: 200px;
    overflow: hidden;
}

.service-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s;
}

.service-card:hover .service-image img {
    transform: scale(1.1);
}

.service-content {
    padding: 20px;
}

.service-content h3 {
    font-size: 1.3rem;
    margin-bottom: 10px;
}

.service-content p {
    color: var(--text-light);
    margin-bottom: 15px;
}

/* Doctors Section */
.doctors {
    background-color: var(--light-color);
    position: relative;
    overflow: hidden;
}

.doctors::before {
    content: '';
    position: absolute;
    top: -100px;
    left: -100px;
    width: 400px;
    height: 400px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(52, 152, 219, 0.1), rgba(46, 204, 113, 0.1));
    z-index: 0;
}

.doctors-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
}

.doctors-header-left {
    max-width: 600px;
}

.doctors-header-left h2 {
    font-size: 2.5rem;
    margin-bottom: 15px;
}

.doctors-header-left h2 span {
    color: var(--primary-color);
}

.doctors-header-left p {
    color: var(--text-light);
}

.doctors-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.doctor-card {
    background-color: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    position: relative;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.doctor-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-light);
}

.doctor-status {
    position: absolute;
    top: 15px;
    left: 15px;
    background-color: var(--success-color);
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.8rem;
    z-index: 1;
}

.doctor-img {
    height: 250px;
    position: relative;
}

.doctor-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.doctor-rating {
    position: absolute;
    bottom: 15px;
    right: 15px;
    background-color: white;
    padding: 5px 10px;
    border-radius: 5px;
    display: flex;
    align-items: center;
    font-weight: 500;
}

.doctor-rating i {
    color: var(--warning-color);
    margin-right: 5px;
}

.doctor-rating span:last-child {
    color: var(--text-light);
    font-size: 0.8rem;
    margin-left: 5px;
}

.doctor-info {
    padding: 20px;
}

.doctor-info h3 {
    font-size: 1.3rem;
    margin-bottom: 5px;
}

.specialization {
    color: var(--primary-color);
    font-weight: 500;
    margin-bottom: 10px;
}

.doctor-info p {
    color: var(--text-light);
    margin-bottom: 15px;
}

.doctor-meta {
    display: flex;
    margin-bottom: 15px;
}

.doctor-meta-item {
    display: flex;
    align-items: center;
    margin-right: 20px;
}

.doctor-meta-item i {
    color: var(--primary-color);
    margin-right: 5px;
}

.doctor-actions {
    display: flex;
    gap: 10px;
}

/* Testimonials Section */
.testimonials {
    background-color: white;
    position: relative;
    overflow: hidden;
}

.testimonials::before {
    content: '';
    position: absolute;
    bottom: -100px;
    right: -100px;
    width: 400px;
    height: 400px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(46, 204, 113, 0.1), rgba(52, 152, 219, 0.1));
    z-index: 0;
}

.testimonials-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
}

.testimonial {
    background-color: white;
    border-radius: 15px;
    padding: 30px;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
    border: 1px solid var(--border-color);
    z-index: 1;
}

.testimonial:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-light);
}

.testimonial-img {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    overflow: hidden;
    margin-bottom: 15px;
    border: 3px solid var(--primary-color);
}

.testimonial-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.testimonial-rating {
    margin-bottom: 15px;
    color: var(--warning-color);
}

.testimonial-content p {
    font-style: italic;
    margin-bottom: 15px;
}

.testimonial-author {
    font-weight: 600;
    margin-bottom: 5px;
}

.testimonial-role {
    color: var(--primary-color);
    font-size: 0.9rem;
}

/* Contact Section */
.contact {
    background-color: var(--light-color);
    position: relative;
    overflow: hidden;
}

.contact::before {
    content: '';
    position: absolute;
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: radial-gradient(circle, var(--primary-light) 0%, rgba(255,255,255,0) 70%);
    opacity: 0.3;
    z-index: 0;
}

.contact-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 50px;
}

.contact-info {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px var(--shadow-color);
}

.contact-info h3 {
    font-size: 1.5rem;
    margin-bottom: 20px;
}

.contact-item {
    display: flex;
    margin-bottom: 20px;
}

.contact-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--primary-light);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 1.2rem;
    color: var(--primary-dark);
}

.contact-details h4 {
    margin-bottom: 5px;
}

.contact-details p {
    color: var(--text-light);
}

.contact-map {
    margin-top: 30px;
    border-radius: 10px;
    overflow: hidden;
    height: 250px;
}

.contact-map iframe {
    width: 100%;
    height: 100%;
    border: none;
}

.contact-form {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px var(--shadow-color);
}

.contact-form h3 {
    font-size: 1.5rem;
    margin-bottom: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 12px;
    font-family: inherit;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
}

.contact-social {
    display: flex;
    justify-content: center;
    margin-top: 30px;
}

.contact-social a {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-light);
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 10px;
    color: var(--primary-dark);
    transition: all 0.3s;
}

.contact-social a:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Page Banner */
.page-banner {
    background: linear-gradient(135deg, var(--primary-dark), var(--secondary-dark));
    padding: 80px 0 60px;
    color: white;
    position: relative;
    overflow: hidden;
    text-align: center;
}

.page-banner::before {
    content: '';
    position: absolute;
    top: -100px;
    right: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    z-index: 0;
}

.page-banner::after {
    content: '';
    position: absolute;
    bottom: -100px;
    left: -100px;
    width: 300px;
    height: 300px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    z-index: 0;
}

.banner-content {
    position: relative;
    z-index: 1;
    max-width: 800px;
    margin: 0 auto;
    animation: fadeInUp 1s ease-out;
}

.banner-content h1 {
    font-size: 3rem;
    margin-bottom: 15px;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.banner-content h1 span {
    color: var(--secondary-light);
    position: relative;
}

.banner-content h1 span::after {
    content: '';
    position: absolute;
    bottom: 5px;
    left: 0;
    width: 100%;
    height: 6px;
    background-color: var(--secondary-color);
    border-radius: 3px;
    z-index: -1;
    opacity: 0.5;
}

.banner-content p {
    font-size: 1.2rem;
    margin-bottom: 25px;
    color: rgba(255, 255, 255, 0.9);
    max-width: 90%;
    margin-left: auto;
    margin-right: auto;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.banner-breadcrumb {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
}

.banner-breadcrumb a {
    color: white;
    text-decoration: none;
    transition: var(--transition);
}

.banner-breadcrumb a:hover {
    color: var(--secondary-light);
}

.banner-breadcrumb i {
    font-size: 0.7rem;
}

/* Doctors Page Styles */
.doctors-page {
    padding: 80px 0;
    background-color: var(--light-color);
}

.search-filter-card {
    background-color: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 40px;
    border: 1px solid var(--border-color);
}

.search-filter-body {
    padding: 30px;
}

.search-filter-grid {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 20px;
    align-items: end;
}

.search-filter-item label {
    font-weight: 500;
    margin-bottom: 10px;
    display: block;
    color: var(--dark-color);
}

.input-icon-wrapper {
    position: relative;
}

.input-icon-wrapper i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

.input-icon-wrapper .form-control {
    padding-left: 45px;
    height: 50px;
    border-radius: 10px;
    border: 1px solid var(--border-color);
    transition: var(--transition);
}

.input-icon-wrapper .form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

.button-item {
    align-self: end;
}

.button-item .btn {
    width: 100%;
    height: 50px;
}

.specialty-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-bottom: 40px;
}

.specialty-filter {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: var(--dark-color);
    transition: var(--transition);
    padding: 15px;
    border-radius: 10px;
    background-color: white;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--border-color);
    min-width: 100px;
}

.specialty-filter:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-color: var(--primary-light);
}

.specialty-filter.active {
    background: var(--gradient-primary);
    color: white;
    border-color: transparent;
}

.specialty-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background-color: var(--light-color);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 10px;
    font-size: 1.2rem;
    color: var(--primary-color);
    transition: var(--transition);
}

.specialty-filter.active .specialty-icon {
    background-color: rgba(255, 255, 255, 0.2);
    color: white;
}

.specialty-filter span {
    font-size: 0.9rem;
    font-weight: 500;
}

.doctors-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.doctors-count h3 {
    font-size: 1.2rem;
    color: var(--dark-color);
    margin: 0;
    display: flex;
    align-items: center;
}

.highlight {
    color: var(--primary-color);
    font-weight: 600;
}

.count-badge {
    font-size: 0.9rem;
    color: #666;
    margin-left: 10px;
    background-color: var(--light-color);
    padding: 5px 10px;
    border-radius: 50px;
}

.doctors-sort .form-control {
    padding: 10px 20px;
    border-radius: 50px;
    border: 1px solid var(--border-color);
    font-size: 0.9rem;
    color: var(--dark-color);
    background-color: white;
}

.no-results-card {
    grid-column: 1 / -1;
    padding: 60px;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border-radius: 15px;
    background-color: white;
    border: 1px solid var(--border-color);
}

.no-results-icon {
    font-size: 4rem;
    color: #ddd;
    margin-bottom: 20px;
}

.no-results-card h3 {
    margin-bottom: 15px;
    color: var(--dark-color);
    font-size: 1.5rem;
}

.no-results-card p {
    color: #666;
    margin-bottom: 25px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 40px;
    gap: 5px;
}

.pagination-arrow,
.pagination-number {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: white;
    color: var(--dark-color);
    text-decoration: none;
    transition: var(--transition);
    font-weight: 500;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.05);
    border: 1px solid var(--border-color);
}

.pagination-arrow:hover,
.pagination-number:hover {
    background-color: var(--primary-light);
    color: var(--primary-dark);
}

.pagination-number.active {
    background: var(--gradient-primary);
    color: white;
    border-color: transparent;
}

.pagination-dots {
    margin: 0 5px;
    color: var(--text-light);
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .container {
        max-width: 960px;
    }
}

@media (max-width: 992px) {
    .container {
        max-width: 720px;
    }

    .hero-content h1,
    .banner-content h1 {
        font-size: 2.8rem;
    }

    .services-grid,
    .doctors-grid,
    .testimonials-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .hero-features {
        grid-template-columns: repeat(2, 1fr);
    }

    .contact-container {
        grid-template-columns: 1fr;
    }

    .search-filter-grid {
        grid-template-columns: 1fr 1fr;
    }

    .search-filter-item.search-item {
        grid-column: span 2;
    }
}

@media (max-width: 768px) {
    .container {
        max-width: 540px;
    }

    .nav-links {
        display: none;
    }

    .mobile-menu {
        display: block;
    }

    .hero-content h1,
    .banner-content h1 {
        font-size: 2.2rem;
    }

    .hero-content p,
    .banner-content p {
        font-size: 1rem;
    }

    .btn-group {
        flex-direction: column;
        gap: 10px;
    }

    .doctors-header {
        flex-direction: column;
        gap: 20px;
    }

    .doctors-header-right {
        align-self: flex-start;
    }

    .search-filter-grid {
        grid-template-columns: 1fr;
    }

    .search-filter-item.search-item {
        grid-column: span 1;
    }

    .specialty-filters {
        justify-content: center;
    }

    .doctors-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }
}

@media (max-width: 576px) {
    .services-grid,
    .doctors-grid,
    .testimonials-grid,
    .hero-features {
        grid-template-columns: 1fr;
    }

    .section-title h2 {
        font-size: 2rem;
    }

    .footer-container {
        grid-template-columns: 1fr;
    }

    .banner-content h1 {
        font-size: 1.8rem;
    }

    .banner-content p {
        font-size: 0.9rem;
    }

    .search-filter-body {
        padding: 20px;
    }

    .specialty-filter {
        min-width: 80px;
        padding: 10px;
    }

    .specialty-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .specialty-filter span {
        font-size: 0.8rem;
    }

    .no-results-card {
        padding: 30px;
    }
}

/* Footer */
.footer {
    background-color: var(--dark-color);
    color: white;
    padding: 80px 0 0;
    position: relative;
    overflow: hidden;
}

.footer::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(52, 152, 219, 0.1) 0%, rgba(44, 62, 80, 0) 70%);
    z-index: 0;
}

.footer::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 300px;
    height: 300px;
    background: radial-gradient(circle, rgba(46, 204, 113, 0.1) 0%, rgba(44, 62, 80, 0) 70%);
    z-index: 0;
}

.footer-container {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 40px;
    margin-bottom: 40px;
    position: relative;
    z-index: 1;
}

.footer-logo {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    animation: fadeIn 1s ease-out;
}

.footer-logo img {
    width: 50px;
    height: 50px;
    margin-right: 15px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.footer-logo-text {
    font-size: 1.8rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.footer-logo-text span {
    color: var(--secondary-color);
}

.footer-col p {
    color: #ccc;
    margin-bottom: 20px;
}

.social-links {
    display: flex;
    margin-bottom: 20px;
}

.social-links li {
    margin-right: 15px;
}

.social-links a {
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    transition: all 0.3s;
}

.social-links a:hover {
    background-color: var(--primary-color);
}

.footer-badges {
    display: flex;
    gap: 15px;
}

.footer-badge {
    display: flex;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.1);
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.8rem;
}

.footer-badge i {
    margin-right: 5px;
    color: var(--primary-color);
}

.footer-col h3 {
    font-size: 1.2rem;
    margin-bottom: 20px;
    position: relative;
    padding-bottom: 10px;
}

.footer-col h3::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background-color: var(--primary-color);
}

.footer-links li {
    margin-bottom: 15px;
    transition: var(--transition);
}

.footer-links li:hover {
    transform: translateX(5px);
}

.footer-links a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
    transition: var(--transition);
    display: flex;
    align-items: center;
    font-weight: 400;
}

.footer-links a:hover {
    color: var(--secondary-light);
}

.footer-links a i {
    margin-right: 10px;
    color: var(--primary-light);
    transition: var(--transition);
}

.footer-links a:hover i {
    color: var(--secondary-light);
}

.footer-contact-item {
    display: flex;
    margin-bottom: 15px;
}

.footer-contact-icon {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 10px;
    font-size: 0.9rem;
}

.newsletter-form {
    display: flex;
    margin-top: 15px;
}

.newsletter-input {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 5px 0 0 5px;
    font-family: inherit;
}

.newsletter-btn {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0 15px;
    border-radius: 0 5px 5px 0;
    cursor: pointer;
    transition: background-color 0.3s;
}

.newsletter-btn:hover {
    background-color: var(--primary-dark);
}

.footer-bottom {
    text-align: center;
    padding: 25px 0;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
    position: relative;
    z-index: 1;
    background-color: rgba(0, 0, 0, 0.2);
}

.footer-bottom a {
    color: var(--secondary-light);
    text-decoration: none;
    transition: var(--transition);
    font-weight: 500;
}

.footer-bottom a:hover {
    color: white;
}

/* Responsive Styles */
@media (max-width: 1024px) {
    .services-grid,
    .doctors-grid,
    .testimonials-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .hero-features {
        grid-template-columns: repeat(2, 1fr);
    }

    .contact-container,
    .footer-container {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .nav-links {
        display: none;
    }

    .mobile-menu {
        display: block;
    }

    .doctors-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .doctors-header-right {
        margin-top: 20px;
    }

    .hero-content h1 {
        font-size: 2.5rem;
    }

    .btn-group {
        flex-direction: column;
    }
}

@media (max-width: 576px) {
    .services-grid,
    .doctors-grid,
    .testimonials-grid,
    .hero-features,
    .contact-container,
    .footer-container {
        grid-template-columns: 1fr;
    }

    .section-title h2 {
        font-size: 2rem;
    }

    .hero-content h1 {
        font-size: 2rem;
    }
}

/* Alert Styles */
.alert {
    padding: 10px 15px;
    border-radius: 5px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
}

.alert i {
    margin-right: 10px;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeeba;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Form Row */
.form-row {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
}

@media (max-width: 576px) {
    .form-row {
        grid-template-columns: 1fr;
    }
}
