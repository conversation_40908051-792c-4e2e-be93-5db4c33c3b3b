/* Admin Doctor Management Styles */

/* Doctor Profile Styles */
.doctor-profile {
    background-color: #fff;
    border-radius: 10px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 30px;
}

.profile-header {
    display: flex;
    align-items: center;
    padding: 25px;
    background-color: #f8f9fc;
    border-bottom: 1px solid #e3e6f0;
    flex-wrap: wrap;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 25px;
    border: 5px solid #fff;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.profile-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.profile-info {
    flex: 1;
    min-width: 250px;
}

.profile-info h2 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.profile-info p {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
    color: #666;
}

.profile-info p i {
    width: 20px;
    margin-right: 10px;
    color: #4e73df;
}

.profile-actions {
    display: flex;
    gap: 10px;
    margin-top: 15px;
    flex-wrap: wrap;
}

.profile-body {
    padding: 25px;
}

.stats-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: #f8f9fc;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
}

.stat-card h4 {
    font-size: 14px;
    font-weight: 600;
    color: #666;
    margin-bottom: 10px;
}

.stat-card p {
    font-size: 24px;
    font-weight: 700;
    color: #4e73df;
    margin: 0;
}

.detail-section {
    margin-bottom: 30px;
}

.detail-section h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e3e6f0;
}

.detail-section p {
    color: #666;
    line-height: 1.6;
}

.detail-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

.detail-item {
    margin-bottom: 15px;
}

.detail-item h4 {
    font-size: 14px;
    font-weight: 600;
    color: #666;
    margin-bottom: 5px;
}

.detail-item p {
    font-size: 16px;
    color: #333;
    margin: 0;
}

/* Form Styles */
.profile-form {
    padding: 25px;
}

.form-section {
    margin-bottom: 30px;
}

.form-section h3 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #e3e6f0;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #666;
    margin-bottom: 8px;
}

.form-control {
    width: 100%;
    padding: 10px 15px;
    font-size: 14px;
    border: 1px solid #e3e6f0;
    border-radius: 5px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #4e73df;
    box-shadow: 0 0 5px rgba(78, 115, 223, 0.1);
    outline: none;
}

textarea.form-control {
    min-height: 120px;
    resize: vertical;
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 15px;
    margin-top: 30px;
}

/* Button Styles */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 600;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    border: none;
}

.btn i {
    margin-right: 8px;
}

.btn-primary {
    background-color: #4e73df;
    color: #fff;
}

.btn-primary:hover {
    background-color: #3756a4;
    color: #fff;
}

.btn-secondary {
    background-color: #858796;
    color: #fff;
}

.btn-secondary:hover {
    background-color: #717384;
    color: #fff;
}

.btn-danger {
    background-color: #e74a3b;
    color: #fff;
}

.btn-danger:hover {
    background-color: #be3c2f;
    color: #fff;
}

.btn-success {
    background-color: #1cc88a;
    color: #fff;
}

.btn-success:hover {
    background-color: #169a6c;
    color: #fff;
}

/* Alert Styles */
.alert {
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 5px;
    display: flex;
    align-items: center;
}

.alert i {
    margin-right: 10px;
    font-size: 18px;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .profile-header {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .profile-avatar {
        margin-right: 0;
        margin-bottom: 20px;
    }
    
    .profile-actions {
        margin-top: 20px;
        width: 100%;
        justify-content: flex-start;
    }
    
    .stats-section {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 768px) {
    .stats-section {
        grid-template-columns: 1fr;
    }
    
    .form-grid {
        grid-template-columns: 1fr;
    }
    
    .form-actions {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}
