/* Form Styles for Doctor Appointment System */

/* Form Container */
.form-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Form Group */
.form-group {
    margin-bottom: 20px;
}

.form-row {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;
}

.form-row .form-group {
    flex: 1;
    margin-bottom: 0;
}

/* Form Label */
.form-label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--text-dark);
}

/* Required Field Indicator */
.form-label.required::after {
    content: '*';
    color: var(--danger-color);
    margin-left: 4px;
}

/* Form Control */
.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    font-family: inherit;
    font-size: 1rem;
    transition: all 0.3s;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
}

/* Form Control with Icon */
.input-with-icon {
    position: relative;
}

.input-with-icon .form-control {
    padding-left: 45px;
}

.input-with-icon i {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: #666;
}

/* Form Control States */
.form-control:invalid {
    border-color: var(--danger-color);
}

.form-control:valid {
    border-color: var(--success-color);
}

/* Form Control Placeholder */
.form-control::placeholder {
    color: #aaa;
}

/* Select Control */
select.form-control {
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23333' d='M6 8.825L1.175 4 2.05 3.125 6 7.075 9.95 3.125 10.825 4 6 8.825z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 15px center;
    padding-right: 40px;
}

/* Textarea Control */
textarea.form-control {
    min-height: 100px;
    resize: vertical;
}

/* Checkbox and Radio */
.checkbox-group, .radio-group {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.checkbox-group input, .radio-group input {
    margin-right: 10px;
}

/* Form Hint */
.form-hint {
    font-size: 0.8rem;
    color: var(--text-light);
    margin-top: 5px;
}

/* Password Toggle */
.password-toggle {
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    cursor: pointer;
    color: #666;
}

/* Form Validation Message */
.validation-message {
    color: var(--danger-color);
    font-size: 0.8rem;
    margin-top: 5px;
    display: none;
}

.form-control:invalid + .validation-message {
    display: block;
}

/* Form Button */
.form-button {
    width: 100%;
    padding: 12px;
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: 5px;
    font-family: inherit;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    display: flex;
    align-items: center;
    justify-content: center;
}

.form-button:hover {
    background: linear-gradient(135deg, var(--primary-dark), var(--secondary-dark));
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

.form-button i {
    margin-left: 8px;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .form-row {
        flex-direction: column;
        gap: 0;
    }
    
    .form-row .form-group {
        margin-bottom: 20px;
    }
}
