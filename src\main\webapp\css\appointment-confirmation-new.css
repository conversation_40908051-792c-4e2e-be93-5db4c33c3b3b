/* Appointment Confirmation Styles */
.main-content {
    margin-left: 220px;
    padding: 30px;
    background-color: #f5f7fa;
    min-height: 100vh;
}

.confirmation-container {
    max-width: 800px;
    margin: 0 auto;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.confirmation-header {
    background-color: #4CAF50;
    color: white;
    padding: 25px;
    text-align: center;
    position: relative;
}

.check-icon {
    width: 60px;
    height: 60px;
    background-color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 15px;
    color: #4CAF50;
    font-size: 30px;
}

.confirmation-header h2 {
    margin: 0;
    font-size: 22px;
    font-weight: 600;
}

.appointment-details {
    padding: 25px;
}

.detail-row {
    display: flex;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.detail-row:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.detail-label {
    width: 150px;
    font-weight: 600;
    color: #555;
}

.detail-value {
    flex: 1;
    color: #333;
}

.status-badge {
    display: inline-block;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: 500;
}

.status-pending {
    background-color: #fff3cd;
    color: #856404;
}

.status-confirmed {
    background-color: #d4edda;
    color: #155724;
}

.status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
}

.doctor-info {
    display: flex;
    align-items: center;
    padding: 25px;
    background-color: #f9f9f9;
    border-bottom: 1px solid #eee;
}

.doctor-avatar {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: #e0e0e0;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 20px;
    font-size: 28px;
    font-weight: 600;
    color: #4CAF50;
    overflow: hidden;
}

.doctor-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.doctor-details {
    flex: 1;
}

.doctor-name {
    margin: 0 0 5px 0;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.doctor-specialty {
    margin: 0;
    color: #666;
}

.important-instructions {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
    padding: 20px;
    margin: 0 25px 25px;
    border-radius: 4px;
}

.important-instructions h3 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #856404;
    font-size: 18px;
    font-weight: 600;
}

.important-instructions ul {
    margin: 0;
    padding-left: 20px;
}

.important-instructions li {
    margin-bottom: 10px;
    color: #856404;
}

.important-instructions li:last-child {
    margin-bottom: 0;
}

.action-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    padding: 25px;
    border-top: 1px solid #eee;
}

.btn {
    display: inline-block;
    padding: 12px 20px;
    border-radius: 6px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.btn i {
    margin-right: 8px;
}

.btn-primary {
    background-color: #4CAF50;
    color: white;
    border: none;
}

.btn-primary:hover {
    background-color: #388E3C;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
    border: none;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.support-footer {
    text-align: center;
    padding: 20px;
    border-top: 1px solid #eee;
    color: #666;
}

/* Responsive styles */
@media (max-width: 992px) {
    .main-content {
        margin-left: 80px;
    }
}

@media (max-width: 768px) {
    .main-content {
        margin-left: 0;
        padding: 20px;
    }
    
    .detail-row {
        flex-direction: column;
    }
    
    .detail-label {
        width: 100%;
        margin-bottom: 5px;
    }
    
    .doctor-info {
        flex-direction: column;
        text-align: center;
    }
    
    .doctor-avatar {
        margin: 0 auto 15px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}
