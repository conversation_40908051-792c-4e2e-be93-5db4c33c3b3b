/* Patient Sidebar Styles */
.sidebar {
    width: 250px;
    height: 100vh;
    background-color: #fff;
    border-right: 1px solid #e0e0e0;
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    padding-top: 20px;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.05);
    z-index: 1000;
    overflow-y: auto;
}

/* Hide any malformed content that might appear as equals symbol */
.sidebar *::before,
.sidebar *::after {
    content: none !important;
}

.user-profile {
    padding: 20px;
    text-align: center;
    width: 100%;
    border-bottom: 1px solid #e0e0e0;
    margin-bottom: 15px;
}

.profile-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin: 0 auto 15px;
    border: 3px solid #f0f0f0;
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    display: block;
}

/* Error handling for images */
.profile-image img[src=""],
.profile-image img:not([src]) {
    display: none;
}

.profile-initials {
    width: 100%;
    height: 100%;
    font-size: 36px;
    font-weight: 600;
    color: white;
    text-transform: uppercase;
    background-color: #4CAF50;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-name {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin-bottom: 5px;
    text-align: center;
}

.user-email, .user-phone {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
    text-align: center;
    word-break: break-word;
}

.sidebar-menu {
    list-style: none;
    padding: 0;
    margin: 0;
    width: 100%;
    padding-bottom: 20px;
}

.sidebar-menu li {
    margin-bottom: 5px;
    width: 100%;
}

.sidebar-menu li a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #333;
    text-decoration: none;
    transition: all 0.3s ease;
    font-size: 15px;
}

.sidebar-menu li a:hover {
    background-color: #f5f5f5;
}

.sidebar-menu li.active a {
    background-color: #f0f0f0;
    color: #4CAF50;
    font-weight: 500;
    border-left: 3px solid #4CAF50;
}

.sidebar-menu li.logout a {
    color: #F44336;
    font-weight: 500;
    margin-top: 10px;
}

.sidebar-menu li a i {
    margin-right: 10px;
    width: 20px;
    text-align: center;
    font-size: 16px;
}

/* Responsive styles */
@media (max-width: 992px) {
    .sidebar {
        width: 80px;
        padding-top: 20px;
    }

    .profile-image {
        width: 60px;
        height: 60px;
        border-width: 3px;
    }

    .profile-initials {
        font-size: 24px;
    }

    .user-name,
    .user-email,
    .user-phone {
        display: none;
    }

    .sidebar-menu li a span {
        display: none;
    }

    .sidebar-menu li a {
        justify-content: center;
        padding: 15px 0;
    }

    .sidebar-menu li a i {
        margin-right: 0;
        font-size: 18px;
    }

    .sidebar-menu li.active a {
        border-left: none;
        border-left: 3px solid #4CAF50;
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        height: auto;
        position: relative;
        padding: 15px;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }

    .profile-image {
        width: 50px;
        height: 50px;
        margin-bottom: 0;
        margin-right: 15px;
    }

    .profile-initials {
        font-size: 20px;
    }

    .user-name {
        display: block;
        margin-bottom: 0;
        margin-right: auto;
    }

    .user-email, .user-phone {
        display: none;
    }

    .sidebar-menu {
        position: absolute;
        top: 80px;
        left: 0;
        width: 100%;
        background-color: #fff;
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
        display: none;
        z-index: 1000;
    }

    .sidebar-menu.active {
        display: block;
    }

    .sidebar-menu li a {
        padding: 12px 20px;
    }

    .sidebar-menu li a span {
        display: inline;
    }

    .sidebar-menu li a i {
        margin-right: 10px;
    }

    /* Mobile menu toggle button */
    .menu-toggle {
        display: block;
        cursor: pointer;
        font-size: 24px;
        color: #333;
    }
}


