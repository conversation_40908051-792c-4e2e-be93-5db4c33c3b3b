/* Dashboard Styles for Doctor Appointment System */

:root {
    /* Primary Colors */
    --primary-color: #4CAF50;
    --primary-dark: #388E3C;
    --primary-light: #A5D6A7;

    /* Secondary Colors */
    --secondary-color: #2196F3;
    --secondary-dark: #1976D2;
    --secondary-light: #BBDEFB;

    /* Status Colors */
    --success-color: #28a745;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;

    /* Neutral Colors */
    --dark-color: #333;
    --light-color: #f4f4f4;
    --text-dark: #333;
    --text-light: #777;
    --border-color: #ddd;
    --shadow-color: rgba(0, 0, 0, 0.1);

    /* Background Colors */
    --bg-light: #f8f9fa;
    --bg-white: #ffffff;
    --bg-dark: #343a40;

    /* Sidebar */
    --sidebar-width: 260px;
    --sidebar-collapsed-width: 70px;
    --sidebar-bg: #2c3e50;
    --sidebar-text: #ecf0f1;
    --sidebar-hover: #34495e;
    --sidebar-active: #1abc9c;

    /* Header */
    --header-height: 70px;
    --header-bg: #ffffff;
    --header-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);

    /* Card */
    --card-bg: #ffffff;
    --card-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --card-border-radius: 10px;
    --card-padding: 20px;

    /* Transitions */
    --transition-speed: 0.3s;
}

/* Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    background-color: var(--bg-light);
    color: var(--text-dark);
    line-height: 1.6;
}

a {
    text-decoration: none;
    color: inherit;
}

ul {
    list-style: none;
}

/* Dashboard Container */
.dashboard-container {
    display: flex;
    min-height: 100vh;
    position: relative;
}

/* Sidebar */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--sidebar-bg);
    color: var(--sidebar-text);
    height: 100vh;
    position: fixed;
    left: 0;
    top: 0;
    z-index: 100;
    transition: width var(--transition-speed);
    overflow-y: auto;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.logo {
    display: flex;
    align-items: center;
    font-size: 1.5rem;
    font-weight: 700;
}

.logo-icon {
    margin-right: 10px;
    font-size: 1.8rem;
    color: var(--sidebar-active);
}

.logo-text span {
    color: var(--sidebar-active);
}

.sidebar-close {
    background: none;
    border: none;
    color: var(--sidebar-text);
    font-size: 1.2rem;
    cursor: pointer;
    display: none;
}

.sidebar-user {
    padding: 20px;
    display: flex;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.user-image {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
    border: 2px solid var(--sidebar-active);
}

.user-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-details h4 {
    font-size: 1rem;
    margin-bottom: 5px;
}

.user-details p {
    font-size: 0.8rem;
    opacity: 0.8;
}

.sidebar-nav {
    padding: 20px 0;
}

.sidebar-nav ul li {
    position: relative;
}

.sidebar-nav ul li a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    transition: all var(--transition-speed);
}

.sidebar-nav ul li a:hover {
    background-color: var(--sidebar-hover);
}

.sidebar-nav ul li.active a {
    background-color: var(--sidebar-active);
    color: #fff;
    border-left: 4px solid #fff;
}

.sidebar-nav ul li a i {
    margin-right: 10px;
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

.sidebar-divider {
    height: 1px;
    background-color: rgba(255, 255, 255, 0.1);
    margin: 10px 0;
}

/* Main Content */
.main-content {
    flex: 1;
    margin-left: var(--sidebar-width);
    transition: margin-left var(--transition-speed);
}

/* Dashboard Header */
.dashboard-header {
    height: var(--header-height);
    background-color: var(--header-bg);
    box-shadow: var(--header-shadow);
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    position: sticky;
    top: 0;
    z-index: 99;
}

.menu-toggle {
    font-size: 1.5rem;
    cursor: pointer;
    display: none;
}

.header-search {
    flex: 1;
    max-width: 400px;
    margin: 0 20px;
}

.header-search form {
    position: relative;
}

.header-search input {
    width: 100%;
    padding: 10px 15px;
    padding-right: 40px;
    border: 1px solid var(--border-color);
    border-radius: 30px;
    font-family: inherit;
    font-size: 0.9rem;
}

.header-search button {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-light);
    cursor: pointer;
    padding: 5px 10px;
}

.header-actions {
    display: flex;
    align-items: center;
}

.notification-bell,
.message-icon {
    position: relative;
    margin-right: 20px;
    font-size: 1.2rem;
    color: var(--text-light);
    cursor: pointer;
}

.badge {
    position: absolute;
    top: -5px;
    right: -5px;
    background-color: var(--danger-color);
    color: white;
    font-size: 0.7rem;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-profile {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.user-profile img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
}

.user-profile span {
    margin-right: 5px;
}

/* Dashboard Content */
.dashboard-content {
    padding: 20px;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
}

.page-header h1 {
    font-size: 1.8rem;
    color: var(--dark-color);
}

.breadcrumb {
    font-size: 0.9rem;
    color: var(--text-light);
}

.breadcrumb a {
    color: var(--primary-color);
}

/* Stats Cards */
.stats-cards {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: var(--card-bg);
    border-radius: var(--card-border-radius);
    box-shadow: var(--card-shadow);
    padding: var(--card-padding);
    position: relative;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.stat-card.primary {
    border-top: 4px solid var(--primary-color);
}

.stat-card.success {
    border-top: 4px solid var(--success-color);
}

.stat-card.warning {
    border-top: 4px solid var(--warning-color);
}

.stat-card.info {
    border-top: 4px solid var(--info-color);
}

.stat-card.danger {
    border-top: 4px solid var(--danger-color);
}

.stat-card-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.8rem;
    margin-bottom: 15px;
}

.stat-card.primary .stat-card-icon {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--primary-color);
}

.stat-card.success .stat-card-icon {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.stat-card.warning .stat-card-icon {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.stat-card.info .stat-card-icon {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

.stat-card.danger .stat-card-icon {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.stat-card-info h2 {
    font-size: 1.8rem;
    margin-bottom: 5px;
}

.stat-card-info p {
    color: var(--text-light);
    font-size: 0.9rem;
}

.stat-card-progress {
    height: 4px;
    background-color: #f0f0f0;
    margin: 15px 0;
    border-radius: 2px;
    overflow: hidden;
}

.progress-bar {
    height: 100%;
    border-radius: 2px;
}

.stat-card.primary .progress-bar {
    background-color: var(--primary-color);
}

.stat-card.success .progress-bar {
    background-color: var(--success-color);
}

.stat-card.warning .progress-bar {
    background-color: var(--warning-color);
}

.stat-card.info .progress-bar {
    background-color: var(--info-color);
}

.stat-card.danger .progress-bar {
    background-color: var(--danger-color);
}

.stat-card-link a {
    color: var(--text-light);
    font-size: 0.9rem;
    display: flex;
    align-items: center;
    transition: color 0.3s;
}

.stat-card-link a i {
    margin-left: 5px;
    transition: transform 0.3s;
}

.stat-card-link a:hover {
    color: var(--dark-color);
}

.stat-card-link a:hover i {
    transform: translateX(3px);
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.grid-card {
    background-color: var(--card-bg);
    border-radius: var(--card-border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
}

.card-header h3 {
    font-size: 1.2rem;
    color: var(--dark-color);
}

.view-all {
    color: var(--primary-color);
    font-size: 0.9rem;
    transition: color 0.3s;
}

.view-all:hover {
    color: var(--primary-dark);
    text-decoration: underline;
}

.card-actions {
    display: flex;
    gap: 10px;
}

.card-body {
    padding: 20px;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 10px 20px;
    border-radius: 5px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
    border: none;
    font-family: inherit;
    font-size: 0.9rem;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-outline {
    border: 1px solid var(--primary-color);
    color: var(--primary-color);
    background-color: transparent;
}

.btn-outline:hover {
    background-color: var(--primary-color);
    color: white;
}

.btn-outline-danger {
    border: 1px solid var(--danger-color);
    color: var(--danger-color);
    background-color: transparent;
}

.btn-outline-danger:hover {
    background-color: var(--danger-color);
    color: white;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 0.8rem;
}

.btn-outline-sm {
    padding: 5px 10px;
    font-size: 0.8rem;
    border: 1px solid var(--border-color);
    color: var(--text-light);
    background-color: transparent;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-outline-sm:hover,
.btn-outline-sm.active {
    background-color: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

.btn-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: rgba(0, 0, 0, 0.05);
    color: var(--text-light);
    border: none;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-icon:hover {
    background-color: var(--primary-color);
    color: white;
}

/* Tables */
.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table thead th {
    padding: 12px 15px;
    text-align: left;
    font-weight: 600;
    color: var(--text-dark);
    border-bottom: 1px solid var(--border-color);
}

.data-table tbody td {
    padding: 12px 15px;
    border-bottom: 1px solid var(--border-color);
    color: var(--text-light);
}

.data-table tbody tr:last-child td {
    border-bottom: none;
}

.data-table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.user-info {
    display: flex;
    align-items: center;
}

.user-info img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
}

.action-buttons {
    display: flex;
    gap: 5px;
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 500;
}

.status-badge.completed {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.status-badge.pending {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.status-badge.cancelled {
    background-color: rgba(220, 53, 69, 0.1);
    color: var(--danger-color);
}

.status-badge.confirmed {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

.status-badge.active {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.status-badge.expired {
    background-color: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

/* Charts */
.chart-card {
    background-color: var(--card-bg);
    border-radius: var(--card-border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    margin-bottom: 30px;
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid var(--border-color);
}

.chart-header h3 {
    font-size: 1.2rem;
    color: var(--dark-color);
}

.chart-body {
    padding: 20px;
}

.chart-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
}

/* Bar Chart */
.bar-chart {
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    height: 250px;
    width: 100%;
    padding: 20px 0;
}

.bar-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 40px;
}

.bar-fill {
    width: 100%;
    background-color: var(--primary-color);
    border-radius: 4px 4px 0 0;
    transition: height 0.5s;
}

.bar-label {
    margin-top: 10px;
    font-size: 0.8rem;
    color: var(--text-light);
}

/* Donut Chart */
.donut-chart {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    position: relative;
    margin: 0 auto 30px;
}

.donut-segment {
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    clip: rect(0, 100px, 200px, 0);
    transform: rotate(calc(3.6deg * var(--offset)));
}

.donut-segment::before {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    clip: rect(0, 200px, 200px, 100px);
    background: var(--bg);
    transform: rotate(calc(3.6deg * var(--value)));
}

.donut-legend {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
}

.legend-item {
    display: flex;
    align-items: center;
}

.legend-color {
    width: 15px;
    height: 15px;
    border-radius: 3px;
    margin-right: 5px;
}

.legend-label {
    font-size: 0.9rem;
    color: var(--text-light);
}

/* Doctor List */
.doctor-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.doctor-card {
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 10px;
    background-color: var(--bg-light);
    transition: transform 0.3s, box-shadow 0.3s;
}

.doctor-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.doctor-avatar {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
    border: 2px solid var(--primary-color);
}

.doctor-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.doctor-info {
    flex: 1;
}

.doctor-info h4 {
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.doctor-info p {
    color: var(--primary-color);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.doctor-rating {
    display: flex;
    align-items: center;
    margin-bottom: 5px;
}

.doctor-rating i {
    color: var(--warning-color);
    font-size: 0.9rem;
    margin-right: 2px;
}

.doctor-rating span {
    color: var(--text-light);
    font-size: 0.8rem;
    margin-left: 5px;
}

.doctor-stats {
    display: flex;
    gap: 15px;
}

.doctor-stats .stat {
    text-align: center;
}

.doctor-stats .stat h5 {
    font-size: 1.1rem;
    color: var(--primary-color);
    margin-bottom: 2px;
}

.doctor-stats .stat p {
    font-size: 0.8rem;
    color: var(--text-light);
}

/* Patient List */
.patient-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.patient-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 10px;
    background-color: var(--bg-light);
    transition: transform 0.3s, box-shadow 0.3s;
}

.patient-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.patient-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
}

.patient-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.patient-info {
    flex: 1;
}

.patient-info h4 {
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.patient-info p {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.patient-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.patient-meta span {
    display: flex;
    align-items: center;
    font-size: 0.8rem;
    color: var(--text-light);
}

.patient-meta span i {
    margin-right: 5px;
    color: var(--primary-color);
}

.patient-actions {
    display: flex;
    gap: 5px;
}

/* Appointment List */
.appointment-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.appointment-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 10px;
    background-color: var(--bg-light);
    transition: transform 0.3s, box-shadow 0.3s;
}

.appointment-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.appointment-date {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-right: 15px;
}

.date-box {
    width: 60px;
    height: 60px;
    border-radius: 10px;
    background-color: var(--primary-color);
    color: white;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    margin-bottom: 5px;
}

.date-box .month {
    font-size: 0.8rem;
    font-weight: 500;
}

.date-box .day {
    font-size: 1.5rem;
    font-weight: 700;
}

.appointment-date .time {
    font-size: 0.8rem;
    color: var(--text-light);
}

.appointment-details {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.appointment-actions {
    display: flex;
    gap: 10px;
}

/* Today's Schedule */
.today-schedule {
    background-color: var(--card-bg);
    border-radius: var(--card-border-radius);
    box-shadow: var(--card-shadow);
    overflow: hidden;
    margin-bottom: 30px;
}

.schedule-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid var(--border-color);
}

.schedule-date h2 {
    font-size: 1.5rem;
    margin-bottom: 5px;
}

.schedule-date p {
    color: var(--text-light);
    font-size: 0.9rem;
}

.schedule-timeline {
    padding: 20px;
    position: relative;
}

.timeline-header {
    margin-bottom: 10px;
}

.time-slots {
    display: flex;
    justify-content: space-between;
}

.time-slots span {
    font-size: 0.8rem;
    color: var(--text-light);
}

.timeline-body {
    position: relative;
    height: 150px;
}

.timeline-ruler {
    position: relative;
    height: 100%;
}

.ruler-line {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--border-color);
}

.current-time {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    align-items: center;
}

.time-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: var(--danger-color);
    margin-bottom: 5px;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.2);
}

.time-label {
    font-size: 0.8rem;
    color: var(--danger-color);
    font-weight: 500;
}

.appointment-slots {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}

.appointment-item {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
}

.appointment-card {
    background-color: var(--bg-white);
    border-radius: 8px;
    padding: 10px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    border-left: 4px solid var(--primary-color);
    min-width: 200px;
}

.appointment-card.active {
    border-left-color: var(--warning-color);
    background-color: rgba(255, 193, 7, 0.05);
}

.appointment-card.completed {
    border-left-color: var(--success-color);
    background-color: rgba(40, 167, 69, 0.05);
}

.appointment-time {
    font-size: 0.8rem;
    color: var(--text-light);
    margin-bottom: 5px;
}

.appointment-patient {
    display: flex;
    align-items: center;
}

.appointment-patient img {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    margin-right: 10px;
}

.appointment-patient h4 {
    font-size: 0.9rem;
    margin-bottom: 2px;
}

.appointment-patient p {
    font-size: 0.8rem;
    color: var(--text-light);
}

/* Welcome Banner */
.welcome-banner {
    display: flex;
    align-items: center;
    background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
    border-radius: var(--card-border-radius);
    overflow: hidden;
    margin-bottom: 30px;
    color: white;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.welcome-content {
    flex: 1;
    padding: 30px;
}

.welcome-content h2 {
    font-size: 1.8rem;
    margin-bottom: 10px;
}

.welcome-content p {
    margin-bottom: 20px;
    opacity: 0.9;
}

.welcome-actions {
    display: flex;
    gap: 15px;
}

.welcome-actions .btn-outline {
    border-color: white;
    color: white;
}

.welcome-actions .btn-outline:hover {
    background-color: white;
    color: var(--primary-color);
}

.welcome-image {
    width: 30%;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
}

.welcome-image img {
    max-width: 100%;
    max-height: 200px;
}

/* Quick Actions */
.quick-actions {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    margin-bottom: 30px;
}

.action-card {
    background-color: var(--card-bg);
    border-radius: var(--card-border-radius);
    box-shadow: var(--card-shadow);
    padding: 20px;
    display: flex;
    align-items: center;
    cursor: pointer;
    transition: transform 0.3s, box-shadow 0.3s;
}

.action-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.action-icon {
    width: 50px;
    height: 50px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-right: 15px;
}

.action-icon.primary {
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--primary-color);
}

.action-icon.success {
    background-color: rgba(40, 167, 69, 0.1);
    color: var(--success-color);
}

.action-icon.warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: var(--warning-color);
}

.action-icon.info {
    background-color: rgba(23, 162, 184, 0.1);
    color: var(--info-color);
}

.action-details h3 {
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.action-details p {
    font-size: 0.8rem;
    color: var(--text-light);
}

/* Health Stats */
.health-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 10px;
    background-color: var(--bg-light);
}

.stat-icon {
    width: 45px;
    height: 45px;
    border-radius: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    margin-right: 15px;
}

.stat-details {
    flex: 1;
}

.stat-details h4 {
    font-size: 0.9rem;
    margin-bottom: 5px;
    color: var(--text-light);
}

.stat-value {
    display: flex;
    align-items: baseline;
    margin-bottom: 5px;
}

.stat-value .value {
    font-size: 1.2rem;
    font-weight: 600;
    margin-right: 5px;
}

.stat-value .unit {
    font-size: 0.8rem;
    color: var(--text-light);
}

.stat-trend {
    display: flex;
    align-items: center;
    font-size: 0.8rem;
}

.stat-trend.up {
    color: var(--success-color);
}

.stat-trend.down {
    color: var(--danger-color);
}

.stat-trend.stable {
    color: var(--warning-color);
}

.stat-trend i {
    margin-right: 5px;
}

.health-chart {
    margin-top: 30px;
}

.chart-title {
    margin-bottom: 15px;
}

.chart-title h4 {
    font-size: 1rem;
    color: var(--text-dark);
}

.line-chart {
    position: relative;
    height: 150px;
    padding: 20px 0;
}

.chart-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}

.chart-line {
    width: 100%;
    height: 1px;
    background-color: rgba(0, 0, 0, 0.05);
}

.chart-data {
    position: relative;
    height: 100%;
}

.data-point {
    position: absolute;
    left: calc(var(--x));
    bottom: calc(var(--y));
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--primary-color);
    transform: translate(-50%, 50%);
    cursor: pointer;
}

.data-point:hover .tooltip {
    opacity: 1;
    transform: translateY(-10px);
}

.tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--dark-color);
    color: white;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    transition: all 0.3s;
    pointer-events: none;
}

.data-line {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 2px;
    background-color: var(--primary-color);
    opacity: 0.3;
}

.chart-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
}

.chart-labels span {
    font-size: 0.8rem;
    color: var(--text-light);
}

/* Medical Records */
.medical-records {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.record-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 10px;
    background-color: var(--bg-light);
    transition: transform 0.3s, box-shadow 0.3s;
}

.record-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.record-icon {
    width: 45px;
    height: 45px;
    border-radius: 10px;
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    margin-right: 15px;
}

.record-details {
    flex: 1;
}

.record-details h4 {
    font-size: 1rem;
    margin-bottom: 5px;
}

.record-details p {
    font-size: 0.8rem;
    color: var(--text-light);
}

.record-actions {
    display: flex;
    gap: 5px;
}

/* Prescriptions */
.prescriptions {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.prescription-item {
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 10px;
    background-color: var(--bg-light);
    transition: transform 0.3s, box-shadow 0.3s;
}

.prescription-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.prescription-icon {
    width: 45px;
    height: 45px;
    border-radius: 10px;
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    margin-right: 15px;
}

.prescription-details {
    flex: 1;
}

.prescription-details h4 {
    font-size: 1rem;
    margin-bottom: 5px;
}

.prescription-details p {
    font-size: 0.8rem;
    color: var(--text-light);
    margin-bottom: 5px;
}

.prescription-meta {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
}

.prescription-meta span {
    display: flex;
    align-items: center;
    font-size: 0.8rem;
    color: var(--text-light);
}

.prescription-meta span i {
    margin-right: 5px;
    color: var(--primary-color);
}

.prescription-status {
    margin-left: 15px;
}

.prescription-reminder {
    display: flex;
    align-items: center;
    padding: 15px;
    border-radius: 10px;
    background-color: rgba(76, 175, 80, 0.05);
    border: 1px dashed var(--primary-color);
}

.reminder-icon {
    width: 45px;
    height: 45px;
    border-radius: 10px;
    background-color: rgba(76, 175, 80, 0.1);
    color: var(--primary-color);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    margin-right: 15px;
}

.reminder-content {
    flex: 1;
}

.reminder-content h4 {
    font-size: 1rem;
    margin-bottom: 5px;
}

.reminder-content p {
    font-size: 0.8rem;
    color: var(--text-light);
    margin-bottom: 10px;
}

/* Messages */
.message-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 20px;
}

.message-item {
    display: flex;
    padding: 15px;
    border-radius: 10px;
    background-color: var(--bg-light);
    transition: transform 0.3s, box-shadow 0.3s;
}

.message-item.unread {
    background-color: rgba(76, 175, 80, 0.05);
    border-left: 3px solid var(--primary-color);
}

.message-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.message-avatar {
    width: 45px;
    height: 45px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
}

.message-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.message-content {
    flex: 1;
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.message-header h4 {
    font-size: 1rem;
}

.message-time {
    font-size: 0.8rem;
    color: var(--text-light);
}

.message-content p {
    font-size: 0.9rem;
    color: var(--text-light);
}

.quick-reply {
    margin-top: 20px;
}

.quick-reply form {
    display: flex;
    gap: 10px;
}

.quick-reply input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid var(--border-color);
    border-radius: 30px;
    font-family: inherit;
    font-size: 0.9rem;
}

.quick-reply button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: var(--primary-color);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.3s;
}

.quick-reply button:hover {
    background-color: var(--primary-dark);
}

/* Performance Chart */
.performance-chart {
    margin-bottom: 30px;
}

.chart-container {
    margin-bottom: 20px;
}

.chart-bars {
    display: flex;
    justify-content: space-between;
    height: 250px;
    padding: 20px 0;
}

.chart-bar {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.bar-group {
    height: 100%;
    display: flex;
    align-items: flex-end;
    gap: 5px;
}

.bar-group .bar-item {
    width: 20px;
    background-color: var(--primary-color);
    border-radius: 4px 4px 0 0;
    position: relative;
}

.bar-group .bar-item::after {
    content: attr(data-value);
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 0.8rem;
    color: var(--text-light);
}

.bar-group .bar-item.secondary {
    background-color: var(--secondary-color);
}

.chart-legend {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 20px;
}

.legend-item {
    display: flex;
    align-items: center;
}

.legend-color {
    width: 15px;
    height: 15px;
    border-radius: 3px;
    margin-right: 5px;
}

.legend-color.primary {
    background-color: var(--primary-color);
}

.legend-color.secondary {
    background-color: var(--secondary-color);
}

.performance-stats {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 15px;
}

/* Responsive Styles */
@media (max-width: 1200px) {
    .stats-cards,
    .quick-actions,
    .health-stats,
    .performance-stats {
        grid-template-columns: repeat(2, 1fr);
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 992px) {
    .sidebar {
        left: -100%;
        z-index: 1000;
    }

    .sidebar.active {
        left: 0;
    }

    .main-content {
        margin-left: 0;
    }

    .menu-toggle {
        display: block;
    }

    .sidebar-close {
        display: block;
    }

    .welcome-banner {
        flex-direction: column;
    }

    .welcome-image {
        width: 100%;
        padding: 0 20px 20px;
    }
}

@media (max-width: 768px) {
    .stats-cards,
    .quick-actions,
    .health-stats,
    .performance-stats {
        grid-template-columns: 1fr;
    }

    .header-search {
        display: none;
    }

    .appointment-details {
        flex-direction: column;
        align-items: flex-start;
    }

    .appointment-actions {
        margin-top: 10px;
    }

    .welcome-actions {
        flex-direction: column;
    }
}

@media (max-width: 576px) {
    .dashboard-header {
        padding: 0 10px;
    }

    .dashboard-content {
        padding: 10px;
    }

    .page-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .breadcrumb {
        margin-top: 5px;
    }

    .notification-bell,
    .message-icon {
        margin-right: 10px;
    }

    .user-profile span {
        display: none;
    }
}
