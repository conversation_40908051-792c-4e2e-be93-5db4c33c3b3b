/*
 * Admin Sidebar Fix - Targeted CSS for Admin Interface Only
 * This CSS file specifically fixes the admin sidebar layout and button styling
 * without affecting other parts of the application (doctor, patient interfaces)
 */

/* Target only admin pages by checking for admin-specific elements */
body:has([href*="/admin/"]) .sidebar,
body:has([action*="/admin/"]) .sidebar,
.admin-layout .sidebar,
[data-admin="true"] .sidebar {
    width: 250px !important;
    height: 100vh !important;
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    z-index: 1000 !important;
    background-color: #fff !important;
    box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1) !important;
    overflow-y: auto !important;
    overflow-x: hidden !important;
    transition: all 0.3s ease !important;
    padding: 0 !important;
    margin: 0 !important;
    border-right: 1px solid #e0e0e0 !important;
    float: none !important;
    display: flex !important;
    flex-direction: column !important;
}

/* Fix sidebar header */
body .sidebar .sidebar-header {
    padding: 15px !important;
    text-align: center !important;
    border-bottom: 1px solid #eee !important;
    background-color: #f8f9fa !important;
    margin: 0 !important;
    display: block !important;
}

/* Fix profile info */
body .sidebar .profile-info {
    padding: 10px 0 !important;
    margin: 0 !important;
    display: block !important;
}

/* Fix sidebar menu */
body .sidebar .sidebar-menu {
    list-style: none !important;
    padding: 15px 0 !important;
    margin: 0 !important;
    width: 100% !important;
    display: block !important;
}

/* Fix menu items */
body .sidebar .menu-item {
    margin: 0 0 5px 0 !important;
    padding: 0 !important;
    width: 100% !important;
    position: relative !important;
    display: block !important;
}

/* Fix menu links */
body .sidebar .menu-link {
    display: flex !important;
    align-items: center !important;
    padding: 12px 20px !important;
    color: #555 !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    font-size: 14px !important;
    border-left: 3px solid transparent !important;
    width: 100% !important;
    box-sizing: border-box !important;
    position: relative !important;
    background-color: transparent !important;
}

/* Fix menu link icons */
body .sidebar .menu-link i {
    width: 20px !important;
    margin-right: 10px !important;
    text-align: center !important;
    font-size: 16px !important;
}

/* Fix menu link hover state */
body .sidebar .menu-link:hover {
    background-color: #f5f5f5 !important;
    color: #4CAF50 !important;
    text-decoration: none !important;
}

/* Fix active menu item */
body .sidebar .menu-item.active .menu-link {
    background-color: #f5f5f5 !important;
    color: #4CAF50 !important;
    border-left-color: #4CAF50 !important;
    font-weight: 500 !important;
}

/* Fix main content to not overlap with sidebar */
body .main-content,
body .dashboard-content {
    margin-left: 250px !important;
    padding: 20px !important;
    transition: margin-left 0.3s ease !important;
    width: calc(100% - 250px) !important;
    float: none !important;
    clear: none !important;
    position: relative !important;
    min-height: 100vh !important;
    background-color: #f8f9fc !important;
    box-sizing: border-box !important;
    display: block !important;
}

/* Fix for admin pages specifically */
body .main-content .page-header {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    margin-bottom: 20px !important;
    padding-bottom: 10px !important;
    border-bottom: 1px solid #e0e0e0 !important;
}

body .main-content .header-left {
    display: flex !important;
    align-items: center !important;
    gap: 15px !important;
}

body .main-content .menu-toggle {
    display: none !important;
    background: none !important;
    border: none !important;
    font-size: 18px !important;
    color: #333 !important;
    cursor: pointer !important;
    padding: 5px !important;
    border-radius: 4px !important;
}

body .main-content .menu-toggle:hover {
    background-color: #f5f5f5 !important;
}

/* Responsive styles */
@media (max-width: 992px) {
    body .sidebar {
        width: 70px !important;
    }

    body .sidebar .sidebar-header h3,
    body .sidebar .profile-info,
    body .sidebar .menu-link span {
        display: none !important;
    }

    body .sidebar .menu-link {
        justify-content: center !important;
        padding: 15px 0 !important;
    }

    body .sidebar .menu-link i {
        margin-right: 0 !important;
        font-size: 18px !important;
    }

    body .main-content,
    body .dashboard-content {
        margin-left: 70px !important;
        width: calc(100% - 70px) !important;
    }

    body .main-content .menu-toggle {
        display: block !important;
    }
}

@media (max-width: 768px) {
    body .sidebar {
        width: 0 !important;
        overflow: hidden !important;
        transform: translateX(-100%) !important;
    }

    body .sidebar.active {
        width: 250px !important;
        transform: translateX(0) !important;
    }

    body .sidebar.active .sidebar-header h3,
    body .sidebar.active .profile-info,
    body .sidebar.active .menu-link span {
        display: block !important;
    }

    body .sidebar.active .menu-link {
        justify-content: flex-start !important;
        padding: 12px 20px !important;
    }

    body .sidebar.active .menu-link i {
        margin-right: 10px !important;
    }

    body .main-content,
    body .dashboard-content {
        margin-left: 0 !important;
        width: 100% !important;
        padding: 15px !important;
    }

    body .main-content .menu-toggle {
        display: block !important;
    }

    body .main-content .page-header {
        flex-direction: column !important;
        align-items: flex-start !important;
        gap: 10px !important;
    }
}

/* Additional fixes for admin pages with different layout structures */
body .dashboard-main {
    margin-left: 250px !important;
    width: calc(100% - 250px) !important;
    padding: 0 !important;
    background-color: #f8f9fc !important;
    min-height: 100vh !important;
    box-sizing: border-box !important;
}

body .dashboard-content {
    padding: 20px !important;
    width: 100% !important;
    box-sizing: border-box !important;
}

body .dashboard-nav {
    padding: 15px 20px !important;
    background-color: #fff !important;
    border-bottom: 1px solid #e0e0e0 !important;
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
}

/* Fix for pages using dashboard-container layout */
body .dashboard-container {
    display: flex !important;
    min-height: 100vh !important;
    width: 100% !important;
    position: relative !important;
}

/* Responsive adjustments for dashboard-main layout */
@media (max-width: 992px) {
    body .dashboard-main {
        margin-left: 70px !important;
        width: calc(100% - 70px) !important;
    }
}

@media (max-width: 768px) {
    body .dashboard-main {
        margin-left: 0 !important;
        width: 100% !important;
    }
}
