/* Appointment Rescheduling Styles */

/* Modal Styles - Improved */
.modal {
    display: none;
    position: fixed;
    z-index: 1050; /* Increased z-index to ensure it appears above sidebar */
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    overflow-y: auto;
    animation: fadeIn 0.3s ease;
    padding: 0 15px; /* Add padding for mobile */
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 25px;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
    position: relative;
    transform: translateY(20px);
    animation: slideUp 0.3s ease forwards;
    overflow: hidden; /* Prevent content overflow */
    left: 0; /* Ensure it's not affected by sidebar */
    right: 0; /* Center horizontally */
}

@keyframes slideUp {
    from { transform: translateY(20px); opacity: 0.8; }
    to { transform: translateY(0); opacity: 1; }
}

@keyframes slideDown {
    from { transform: translateY(0); opacity: 1; }
    to { transform: translateY(20px); opacity: 0; }
}

.close {
    position: absolute;
    right: 20px;
    top: 15px;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    color: #666;
    transition: color 0.2s ease;
    z-index: 10; /* Ensure it's above other elements */
    width: 30px; /* Fixed width for better touch target */
    height: 30px; /* Fixed height for better touch target */
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
}

.close:hover {
    color: #F44336;
    background-color: rgba(244, 67, 54, 0.1);
}

.modal-header {
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
    margin-bottom: 20px;
    display: flex;
    align-items: center;
    position: relative; /* For positioning the close button */
    padding-right: 40px; /* Make room for the close button */
}

.modal-header h3 {
    margin: 0;
    color: #333;
    font-size: 1.4rem;
    font-weight: 600;
    line-height: 1.3;
}

.modal-header i {
    margin-right: 10px;
    color: #4CAF50;
    font-size: 1.4rem;
    flex-shrink: 0; /* Prevent icon from shrinking */
}

/* Form Styles - Improved */
.form-group {
    margin-bottom: 20px;
    position: relative; /* For positioning error messages */
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #444;
    font-size: 0.95rem;
}

.form-group input[type="date"],
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-family: inherit;
    font-size: 1rem;
    background-color: #f9f9f9;
    transition: all 0.3s ease;
    box-sizing: border-box; /* Ensure padding doesn't affect width */
}

.form-group input[type="date"]:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
    background-color: #fff;
}

.form-group textarea {
    resize: vertical;
    min-height: 100px;
    font-family: inherit;
    line-height: 1.5;
}

/* Date and Time Selection Styles */
.form-group input[type="date"] {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    padding-right: 30px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%23666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect><line x1="16" y1="2" x2="16" y2="6"></line><line x1="8" y1="2" x2="8" y2="6"></line><line x1="3" y1="10" x2="21" y2="10"></line></svg>');
    background-repeat: no-repeat;
    background-position: calc(100% - 10px) center;
    background-size: 16px;
    height: 44px; /* Fixed height for better alignment */
}

.form-group select {
    appearance: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    padding-right: 30px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%23666" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg>');
    background-repeat: no-repeat;
    background-position: calc(100% - 10px) center;
    background-size: 16px;
    height: 44px; /* Fixed height for better alignment */
    cursor: pointer;
}

/* Add hover effect for select */
.form-group select:hover {
    border-color: #aaa;
    background-color: #f5f5f5;
}

/* Modal Footer */
.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 25px;
    padding-top: 15px;
    border-top: 1px solid #eee;
    flex-wrap: wrap; /* Allow buttons to wrap on small screens */
}

.modal-footer .btn {
    padding: 12px 20px;
    border-radius: 8px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 120px;
    border: none;
    font-size: 0.95rem;
    letter-spacing: 0.5px;
}

.modal-footer .btn i {
    margin-right: 8px;
    font-size: 1rem;
}

.modal-footer .btn-outline {
    background-color: transparent;
    color: #666;
    border: 1px solid #ddd;
}

.modal-footer .btn-outline:hover {
    background-color: #f5f5f5;
    border-color: #ccc;
    color: #333;
}

.modal-footer .btn-outline:active {
    background-color: #eee;
    transform: translateY(1px);
}

.modal-footer .btn-primary {
    background-color: #4CAF50;
    color: white;
}

.modal-footer .btn-primary:hover {
    background-color: #388E3C;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.modal-footer .btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Patient Profile Image Styles */
.patient-profile {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 10px;
    border: 1px solid #eee;
}

.patient-profile h4 {
    margin: 0 0 5px 0 !important;
    font-size: 16px !important;
    color: #333;
    font-weight: 600;
    line-height: 1.2;
}

.patient-profile p {
    margin: 0 !important;
    font-size: 14px !important;
    color: #666 !important;
    line-height: 1.4;
}

.patient-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
    margin-right: 15px;
    border: 3px solid #fff;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    flex-shrink: 0; /* Prevent avatar from shrinking */
    background-color: #f0f0f0;
}

.patient-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.patient-avatar .profile-initials {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #4CAF50;
    color: white;
    font-size: 1.5rem;
    font-weight: 600;
    text-transform: uppercase;
}

/* Error message styling */
.error-message {
    color: #d32f2f;
    font-size: 0.85rem;
    margin-top: 5px;
    display: block;
}

/* Warning message styling */
.warning-message {
    margin: 20px 0;
    padding: 15px;
    background-color: #fff3cd;
    border-radius: 8px;
    border-left: 4px solid #ffc107;
}

.warning-message p {
    margin: 0 0 10px 0;
    font-weight: 500;
    color: #856404;
}

.warning-message p:last-child {
    margin-bottom: 0;
}

/* Loading indicator for form submission */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2100; /* Increased z-index to be above modal and sidebar */
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.3s ease, visibility 0.3s ease;
}

.loading-overlay.active {
    visibility: visible;
    opacity: 1;
}

.loading-spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Form validation styles */
.form-group input:invalid,
.form-group select:invalid,
.form-group textarea:invalid {
    border-color: #ffcccc;
}

.form-group input:focus:invalid,
.form-group select:focus:invalid,
.form-group textarea:focus:invalid {
    border-color: #ff8080;
    box-shadow: 0 0 0 2px rgba(255, 0, 0, 0.1);
}

/* Responsive Styles */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 5% auto;
        padding: 20px;
        max-height: 90vh;
        overflow-y: auto;
        left: 0 !important; /* Force left position */
        right: 0 !important; /* Force right position */
    }

    .modal {
        padding: 0; /* Remove padding on mobile */
        z-index: 2000; /* Even higher z-index on mobile */
        left: 0 !important; /* Force left position */
        right: 0 !important; /* Force right position */
    }

    .modal-header h3 {
        font-size: 1.2rem;
    }

    .modal-footer {
        flex-direction: column-reverse; /* Put primary action on top on mobile */
        gap: 10px;
    }

    .modal-footer .btn {
        width: 100%;
        padding: 14px 20px; /* Larger touch target on mobile */
    }

    /* Improve form elements on mobile */
    .form-group label {
        font-size: 0.9rem;
    }

    .form-group input[type="date"],
    .form-group select {
        height: 48px; /* Larger touch target on mobile */
        font-size: 16px; /* Prevent zoom on iOS */
    }

    .form-group textarea {
        font-size: 16px; /* Prevent zoom on iOS */
    }

    /* Adjust patient profile on mobile */
    .patient-profile {
        padding: 12px;
    }

    .patient-avatar {
        width: 50px;
        height: 50px;
    }
}

/* Fix for iOS date inputs */
@supports (-webkit-touch-callout: none) {
    .form-group input[type="date"] {
        min-height: 44px;
        line-height: 1;
        padding-top: 0;
        padding-bottom: 0;
        display: flex;
        align-items: center;
    }
}
