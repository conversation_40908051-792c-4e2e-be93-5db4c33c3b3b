/* Global Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

a {
    text-decoration: none;
    color: inherit;
}

ul {
    list-style: none;
}

.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Button Styles */
.btn {
    display: inline-block;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    font-weight: 500;
}

.btn-primary {
    background-color: #4CAF50;
    color: white;
}

.btn-primary:hover {
    background-color: #388E3C;
}

.btn-secondary {
    background-color: #2196F3;
    color: white;
}

.btn-secondary:hover {
    background-color: #1976D2;
}

.btn-danger {
    background-color: #F44336;
    color: white;
}

.btn-danger:hover {
    background-color: #D32F2F;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid #4CAF50;
    color: #4CAF50;
}

.btn-outline:hover {
    background-color: #4CAF50;
    color: white;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-control {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
    font-size: 16px;
}

.form-control:focus {
    outline: none;
    border-color: #4CAF50;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-error {
    color: #F44336;
    font-size: 14px;
    margin-top: 5px;
}

/* Alert Styles */
.alert {
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.alert-warning {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeeba;
}

.alert-info {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* Card Styles */
.card {
    background-color: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.card-header {
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
    margin-bottom: 15px;
}

.card-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #333;
}

.card-body {
    padding: 15px 0;
}

.card-footer {
    border-top: 1px solid #eee;
    padding-top: 15px;
    margin-top: 15px;
}

/* Table Styles */
.table {
    width: 100%;
    border-collapse: collapse;
}

.table th, .table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.table th {
    font-weight: 600;
    background-color: #f9f9f9;
}

.table tbody tr:hover {
    background-color: #f5f5f5;
}

/* Responsive Styles */
@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }
    
    .card {
        padding: 15px;
    }
    
    .table th, .table td {
        padding: 8px 10px;
    }
}
