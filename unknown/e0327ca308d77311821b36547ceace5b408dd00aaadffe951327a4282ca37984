/* Common Styles for the Doctor Appointment System */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

a {
    text-decoration: none;
    color: #1976d2;
}

ul {
    list-style: none;
}

/* Container */
.container {
    width: 100%;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #1976d2;
    color: white;
}

.btn-primary:hover {
    background-color: #1565c0;
}

.btn-secondary {
    background-color: #f5f5f5;
    color: #333;
    border: 1px solid #ddd;
}

.btn-secondary:hover {
    background-color: #e0e0e0;
}

.btn-success {
    background-color: #4caf50;
    color: white;
}

.btn-success:hover {
    background-color: #43a047;
}

.btn-danger {
    background-color: #f44336;
    color: white;
}

.btn-danger:hover {
    background-color: #e53935;
}

.btn-sm {
    padding: 6px 12px;
    font-size: 14px;
}

.btn-lg {
    padding: 12px 24px;
    font-size: 16px;
}

/* Form Elements */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
}

.form-control {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-control:focus {
    border-color: #1976d2;
    outline: none;
}

.form-select {
    width: 100%;
    padding: 10px 15px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23333' d='M6 8.825L1.175 4 2.05 3.125 6 7.075 9.95 3.125 10.825 4z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 15px center;
}

.form-check {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.form-check-input {
    margin-right: 10px;
}

/* Cards */
.card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
    margin-bottom: 20px;
}

.card-header {
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    background-color: #f8f9fa;
}

.card-title {
    font-size: 18px;
    font-weight: 600;
    margin: 0;
}

.card-body {
    padding: 20px;
}

.card-footer {
    padding: 15px 20px;
    border-top: 1px solid #f0f0f0;
    background-color: #f8f9fa;
}

/* Tables */
.table {
    width: 100%;
    border-collapse: collapse;
}

.table th, .table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.table th {
    font-weight: 600;
    background-color: #f8f9fa;
}

.table tr:hover {
    background-color: #f9f9f9;
}

/* Alerts */
.alert {
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.alert-success {
    background-color: #e8f5e9;
    color: #2e7d32;
    border-left: 4px solid #4caf50;
}

.alert-info {
    background-color: #e3f2fd;
    color: #0d47a1;
    border-left: 4px solid #2196f3;
}

.alert-warning {
    background-color: #fff8e1;
    color: #ff8f00;
    border-left: 4px solid #ffc107;
}

.alert-danger {
    background-color: #ffebee;
    color: #c62828;
    border-left: 4px solid #f44336;
}

/* Badges */
.badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.badge-primary {
    background-color: #e3f2fd;
    color: #1976d2;
}

.badge-success {
    background-color: #e8f5e9;
    color: #4caf50;
}

.badge-warning {
    background-color: #fff8e1;
    color: #ff8f00;
}

.badge-danger {
    background-color: #ffebee;
    color: #f44336;
}

/* Utilities */
.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.text-left {
    text-align: left;
}

.d-flex {
    display: flex;
}

.justify-content-between {
    justify-content: space-between;
}

.align-items-center {
    align-items: center;
}

.mt-1 {
    margin-top: 5px;
}

.mt-2 {
    margin-top: 10px;
}

.mt-3 {
    margin-top: 15px;
}

.mb-1 {
    margin-bottom: 5px;
}

.mb-2 {
    margin-bottom: 10px;
}

.mb-3 {
    margin-bottom: 15px;
}

.ml-1 {
    margin-left: 5px;
}

.ml-2 {
    margin-left: 10px;
}

.ml-3 {
    margin-left: 15px;
}

.mr-1 {
    margin-right: 5px;
}

.mr-2 {
    margin-right: 10px;
}

.mr-3 {
    margin-right: 15px;
}

.p-1 {
    padding: 5px;
}

.p-2 {
    padding: 10px;
}

.p-3 {
    padding: 15px;
}

/* Responsive Utilities */
@media (max-width: 992px) {
    .d-lg-none {
        display: none;
    }
    
    .d-lg-block {
        display: block;
    }
    
    .d-lg-flex {
        display: flex;
    }
}

@media (max-width: 768px) {
    .d-md-none {
        display: none;
    }
    
    .d-md-block {
        display: block;
    }
    
    .d-md-flex {
        display: flex;
    }
}

@media (max-width: 576px) {
    .d-sm-none {
        display: none;
    }
    
    .d-sm-block {
        display: block;
    }
    
    .d-sm-flex {
        display: flex;
    }
}
