/* Admin Dashboard Styles */

/* General Layout */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    background-color: #f5f5f5;
    color: #333;
}

.dashboard-container {
    display: flex;
    min-height: 100vh;
}

/* Sidebar Styles */
.dashboard-sidebar {
    width: 250px;
    background-color: #fff;
    border-right: 1px solid #e0e0e0;
    transition: all 0.3s ease;
    z-index: 1000;
}

.sidebar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.sidebar-logo {
    display: flex;
    align-items: center;
}

.sidebar-logo img {
    width: 40px;
    height: 40px;
    margin-right: 10px;
}

.profile-initials {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #4e54c8;
    color: white;
    font-size: 18px;
    font-weight: 700;
    margin-right: 10px;
}

.sidebar-logo h2 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.sidebar-logo h2 span {
    color: #4e73df;
}

.sidebar-close {
    display: none;
    background: none;
    border: none;
    color: #777;
    font-size: 18px;
    cursor: pointer;
}

.sidebar-menu {
    padding: 20px 0;
}

.sidebar-menu ul {
    list-style: none;
}

.sidebar-menu li {
    margin-bottom: 5px;
}

.sidebar-menu a {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #555;
    text-decoration: none;
    transition: all 0.3s ease;
}

.sidebar-menu a:hover {
    background-color: #f8f9fc;
    color: #4e73df;
}

.sidebar-menu a.active {
    background-color: #f8f9fc;
    color: #4e73df;
    border-left: 4px solid #4e73df;
}

.sidebar-menu i {
    margin-right: 10px;
    font-size: 18px;
    width: 20px;
    text-align: center;
}

.sidebar-menu .logout {
    margin-top: 30px;
    border-top: 1px solid #f0f0f0;
    padding-top: 15px;
}

.sidebar-menu .logout a {
    color: #e74a3b;
}

.sidebar-menu .logout a:hover {
    background-color: #fdecea;
}

/* Main Content Styles */
.dashboard-main {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.dashboard-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #fff;
    border-bottom: 1px solid #e0e0e0;
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    color: #555;
    font-size: 20px;
    cursor: pointer;
}

.nav-right {
    display: flex;
    align-items: center;
}

.search-box {
    position: relative;
    margin-right: 20px;
}

.search-box input {
    width: 250px;
    padding: 8px 15px;
    padding-left: 35px;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 14px;
}

.search-box i {
    position: absolute;
    left: 10px;
    top: 50%;
    transform: translateY(-50%);
    color: #777;
}

.nav-notifications, .nav-messages {
    margin-right: 20px;
    position: relative;
}

.icon-badge {
    position: relative;
    cursor: pointer;
}

.icon-badge i {
    font-size: 18px;
    color: #555;
}

.badge {
    position: absolute;
    top: -8px;
    right: -8px;
    background-color: #e74a3b;
    color: white;
    font-size: 10px;
    width: 16px;
    height: 16px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.nav-user {
    display: flex;
    align-items: center;
    cursor: pointer;
}

.nav-user img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
}

.user-info h4 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 2px;
}

.user-info p {
    font-size: 12px;
    color: #777;
}

/* Dashboard Content */
.dashboard-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.page-header {
    margin-bottom: 25px;
}

.page-header h1 {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.page-header p {
    font-size: 14px;
    color: #777;
}

/* Search Container */
.search-container {
    margin-bottom: 25px;
}

.search-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
}

.search-form {
    display: flex;
    max-width: 600px;
    flex: 1;
}

.search-form input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 4px 0 0 4px;
    font-size: 14px;
}

.search-button {
    padding: 10px 20px;
    background-color: #4e73df;
    color: white;
    border: none;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    font-weight: 500;
}

.date-display {
    display: flex;
    align-items: center;
    font-size: 14px;
    color: #555;
    background-color: #fff;
    padding: 8px 15px;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
}

.date-display i {
    margin-left: 10px;
    color: #4e73df;
}

.today-date {
    font-weight: 600;
    margin: 0 5px;
}

.status-section h2 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.status-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
}

.status-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 20px;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.status-number {
    font-size: 32px;
    font-weight: 700;
    color: #4e73df;
    margin-bottom: 5px;
}

.status-label {
    font-size: 16px;
    color: #555;
    font-weight: 500;
}

.status-icon {
    position: absolute;
    top: 15px;
    right: 15px;
    color: #e0e0e0;
    font-size: 20px;
}

/* Upcoming Section */
.upcoming-section {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.upcoming-appointments, .upcoming-sessions {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    padding: 20px;
}

.upcoming-section h2 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 10px;
    color: #333;
}

.upcoming-section p {
    font-size: 14px;
    color: #777;
    margin-bottom: 20px;
}

.appointment-table, .session-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.activity-details h4 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 5px;
}

.activity-details p {
    font-size: 13px;
    color: #777;
    margin-bottom: 5px;
}

.activity-time {
    font-size: 12px;
    color: #999;
}

.red {
    background-color: #e74a3b;
}

/* Tables */
.tables-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 25px;
}

.table-card {
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
}

.table-header h3 {
    font-size: 16px;
    font-weight: 600;
}

.table-responsive {
    overflow-x: auto;
}

.table-responsive table {
    width: 100%;
    border-collapse: collapse;
}

.table-responsive th, .table-responsive td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.table-responsive th {
    font-weight: 600;
    color: #333;
    background-color: #f8f9fc;
}

.user-info {
    display: flex;
    align-items: center;
}

.user-info img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
}

.user-info h4 {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 2px;
}

.user-info p {
    font-size: 12px;
    color: #777;
}

.status-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

.active {
    background-color: #e6f7ef;
    color: #1cc88a;
}

.inactive {
    background-color: #fdecea;
    color: #e74a3b;
}

.action-buttons {
    display: flex;
    gap: 5px;
}

.btn-icon {
    width: 30px;
    height: 30px;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
}

.view {
    background-color: #4e73df;
}

.edit {
    background-color: #1cc88a;
}

.delete {
    background-color: #e74a3b;
}

/* Footer */
.dashboard-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #fff;
    border-top: 1px solid #e0e0e0;
    font-size: 12px;
    color: #777;
}

/* Responsive Styles */
@media (max-width: 992px) {
    .dashboard-sidebar {
        position: fixed;
        left: -250px;
        height: 100%;
    }
    

    .dashboard-sidebar.active {
        left: 0;
    }
    

    .sidebar-close {
        display: block;
    }
    

    .menu-toggle {
        display: block;
    } 

    .charts-container, .tables-container, .upcoming-section {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .status-cards {
        grid-template-columns: repeat(2, 1fr);
    }
    

    .search-box input {
        width: 180px;
    }
    

    .nav-user .user-info {
        display: none;
    }

    .appointment-table, .session-table {
        font-size: 13px;
    }

    .appointment-table th, .appointment-table td,
    .session-table th, .session-table td {
        padding: 8px 10px;
    }

    .search-row {
        flex-direction: column;
        align-items: flex-start;
    }

    .search-form {
        width: 100%;
    }

    .date-display {
        align-self: flex-end;
    }
}

@media (max-width: 576px) {
    .search-box {
        display: none;
    }

    .status-cards {
        grid-template-columns: 1fr;
    }

    .search-form {
        flex-direction: column;
    }

    .search-form input {
        border-radius: 4px;
        margin-bottom: 10px;
    }

    .search-button {
        border-radius: 4px;
    }
}