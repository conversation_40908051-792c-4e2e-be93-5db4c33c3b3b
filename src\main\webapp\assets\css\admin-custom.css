/* Custom CSS to replace <PERSON><PERSON><PERSON> in admin dashboard */

/* Reset and Base Styles */
*, *::before, *::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  /* Colors */
  --primary: #3498db;
  --primary-dark: #2980b9;
  --primary-light: #a8d4f5;
  --secondary: #2ecc71;
  --secondary-dark: #27ae60;
  --secondary-light: #a9f5bc;
  --success: #2ecc71;
  --danger: #e74c3c;
  --warning: #f39c12;
  --info: #3498db;
  --light: #f8f9fa;
  --dark: #343a40;
  --white: #ffffff;
  --black: #000000;
  --gray: #6c757d;
  --gray-dark: #343a40;
  --gray-light: #f8f9fa;

  /* Spacing */
  --spacing-1: 0.25rem;
  --spacing-2: 0.5rem;
  --spacing-3: 1rem;
  --spacing-4: 1.5rem;
  --spacing-5: 3rem;

  /* Typography */
  --font-family-sans-serif: 'Poppins', -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON>l, sans-serif;
  --font-family-monospace: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
  --font-size-base: 1rem;
  --font-size-sm: 0.875rem;
  --font-size-lg: 1.25rem;

  /* Border radius */
  --border-radius: 0.25rem;
  --border-radius-lg: 0.5rem;
  --border-radius-sm: 0.2rem;

  /* Transitions */
  --transition: all 0.3s ease;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
}

body {
  font-family: var(--font-family-sans-serif);
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--dark);
  background-color: var(--light);
  margin: 0;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin-bottom: var(--spacing-2);
  font-weight: 500;
  line-height: 1.2;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.75rem; }
h4 { font-size: 1.5rem; }
h5 { font-size: 1.25rem; }
h6 { font-size: 1rem; }

p {
  margin-bottom: var(--spacing-3);
}

a {
  color: var(--primary);
  text-decoration: none;
  transition: var(--transition);
}

a:hover {
  color: var(--primary-dark);
  text-decoration: underline;
}

/* Container */
.container {
  width: 100%;
  padding-right: var(--spacing-3);
  padding-left: var(--spacing-3);
  margin-right: auto;
  margin-left: auto;
}

@media (min-width: 576px) {
  .container {
    max-width: 540px;
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 720px;
  }
}

@media (min-width: 992px) {
  .container {
    max-width: 960px;
  }
}

@media (min-width: 1200px) {
  .container {
    max-width: 1140px;
  }
}

/* Grid System */
.row {
  display: flex;
  flex-wrap: wrap;
  margin-right: -15px;
  margin-left: -15px;
}

.col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6, 
.col-7, .col-8, .col-9, .col-10, .col-11, .col-12, 
.col-sm, .col-md, .col-lg, .col-xl {
  position: relative;
  width: 100%;
  padding-right: 15px;
  padding-left: 15px;
}

.col { flex-basis: 0; flex-grow: 1; max-width: 100%; }
.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* Buttons */
.btn {
  display: inline-block;
  font-weight: 400;
  text-align: center;
  white-space: nowrap;
  vertical-align: middle;
  user-select: none;
  border: 1px solid transparent;
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  line-height: 1.5;
  border-radius: var(--border-radius);
  transition: var(--transition);
  cursor: pointer;
}

.btn:hover, .btn:focus {
  text-decoration: none;
}

.btn-primary {
  color: var(--white);
  background-color: var(--primary);
  border-color: var(--primary);
}

.btn-primary:hover {
  color: var(--white);
  background-color: var(--primary-dark);
  border-color: var(--primary-dark);
}

.btn-secondary {
  color: var(--white);
  background-color: var(--secondary);
  border-color: var(--secondary);
}

.btn-secondary:hover {
  color: var(--white);
  background-color: var(--secondary-dark);
  border-color: var(--secondary-dark);
}

.btn-success {
  color: var(--white);
  background-color: var(--success);
  border-color: var(--success);
}

.btn-danger {
  color: var(--white);
  background-color: var(--danger);
  border-color: var(--danger);
}

.btn-warning {
  color: var(--dark);
  background-color: var(--warning);
  border-color: var(--warning);
}

.btn-info {
  color: var(--white);
  background-color: var(--info);
  border-color: var(--info);
}

.btn-light {
  color: var(--dark);
  background-color: var(--light);
  border-color: var(--light);
}

.btn-dark {
  color: var(--white);
  background-color: var(--dark);
  border-color: var(--dark);
}

.btn-outline-primary {
  color: var(--primary);
  background-color: transparent;
  border-color: var(--primary);
}

.btn-outline-primary:hover {
  color: var(--white);
  background-color: var(--primary);
  border-color: var(--primary);
}

/* Tables */
.table {
  width: 100%;
  margin-bottom: 1rem;
  color: var(--dark);
  border-collapse: collapse;
}

.table th,
.table td {
  padding: 0.75rem;
  vertical-align: top;
  border-top: 1px solid #dee2e6;
}

.table thead th {
  vertical-align: bottom;
  border-bottom: 2px solid #dee2e6;
}

.table tbody + tbody {
  border-top: 2px solid #dee2e6;
}

.table-sm th,
.table-sm td {
  padding: 0.3rem;
}

.table-bordered {
  border: 1px solid #dee2e6;
}

.table-bordered th,
.table-bordered td {
  border: 1px solid #dee2e6;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.05);
}

.table-hover tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.075);
}

/* Forms */
.form-group {
  margin-bottom: 1rem;
}

.form-control {
  display: block;
  width: 100%;
  height: calc(1.5em + 0.75rem + 2px);
  padding: 0.375rem 0.75rem;
  font-size: 1rem;
  font-weight: 400;
  line-height: 1.5;
  color: #495057;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #ced4da;
  border-radius: 0.25rem;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
  color: #495057;
  background-color: #fff;
  border-color: var(--primary-light);
  outline: 0;
  box-shadow: 0 0 0 0.2rem rgba(52, 152, 219, 0.25);
}

/* Utilities */
.mt-1 { margin-top: var(--spacing-1) !important; }
.mt-2 { margin-top: var(--spacing-2) !important; }
.mt-3 { margin-top: var(--spacing-3) !important; }
.mt-4 { margin-top: var(--spacing-4) !important; }
.mt-5 { margin-top: var(--spacing-5) !important; }

.mb-1 { margin-bottom: var(--spacing-1) !important; }
.mb-2 { margin-bottom: var(--spacing-2) !important; }
.mb-3 { margin-bottom: var(--spacing-3) !important; }
.mb-4 { margin-bottom: var(--spacing-4) !important; }
.mb-5 { margin-bottom: var(--spacing-5) !important; }

.ml-1 { margin-left: var(--spacing-1) !important; }
.ml-2 { margin-left: var(--spacing-2) !important; }
.ml-3 { margin-left: var(--spacing-3) !important; }
.ml-4 { margin-left: var(--spacing-4) !important; }
.ml-5 { margin-left: var(--spacing-5) !important; }

.mr-1 { margin-right: var(--spacing-1) !important; }
.mr-2 { margin-right: var(--spacing-2) !important; }
.mr-3 { margin-right: var(--spacing-3) !important; }
.mr-4 { margin-right: var(--spacing-4) !important; }
.mr-5 { margin-right: var(--spacing-5) !important; }

.p-1 { padding: var(--spacing-1) !important; }
.p-2 { padding: var(--spacing-2) !important; }
.p-3 { padding: var(--spacing-3) !important; }
.p-4 { padding: var(--spacing-4) !important; }
.p-5 { padding: var(--spacing-5) !important; }

.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.d-flex { display: flex !important; }
.d-block { display: block !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-none { display: none !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }

.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }
