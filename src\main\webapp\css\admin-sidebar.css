/* Admin Sidebar Styles */
:root {
    --sidebar-width: 220px;
    --sidebar-collapsed-width: 70px;
    --primary-color: #4e73df;
    --primary-dark: #3756a4;
    --text-color: #333;
    --text-light: #666;
    --border-color: #e0e0e0;
    --bg-light: #f8f9fc;
    --bg-white: #ffffff;
    --shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* Sidebar Container */
.sidebar {
    width: var(--sidebar-width);
    background-color: var(--bg-white);
    border-right: 1px solid var(--border-color);
    height: 100vh;
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1000;
    transition: all 0.3s ease;
    box-shadow: var(--shadow);
    overflow-y: auto;
}

/* Sidebar Header */
.sidebar-header {
    padding: 15px;
    border-bottom: 1px solid var(--border-color);
    text-align: center;
}

.sidebar-header h3 {
    font-size: 18px;
    font-weight: 600;
    color: var(--primary-color);
    margin-bottom: 10px;
}

.profile-info {
    padding: 5px 0;
}

.profile-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    margin: 0 auto 10px;
    overflow: hidden;
    border: 2px solid var(--primary-color);
}

.profile-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.user-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 2px;
}

.user-role {
    font-size: 12px;
    color: var(--text-light);
}

/* Sidebar Menu */
.sidebar-menu {
    list-style: none;
    padding: 15px 0;
    margin: 0;
}

.menu-item {
    margin-bottom: 5px;
}

.menu-link {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.3s ease;
    border-left: 3px solid transparent;
}

.menu-link:hover {
    background-color: var(--bg-light);
    color: var(--primary-color);
}

.menu-item.active .menu-link {
    background-color: var(--bg-light);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
}

.menu-link i {
    width: 20px;
    margin-right: 10px;
    text-align: center;
    font-size: 16px;
}

.menu-link span {
    flex: 1;
}

.badge {
    background-color: var(--primary-color);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 5px;
}

/* Main Content Adjustment */
.main-content {
    margin-left: var(--sidebar-width);
    padding: 20px;
    transition: margin-left 0.3s ease;
}

/* Header Styles */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid var(--border-color);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 15px;
}

.menu-toggle {
    display: none;
    background: none;
    border: none;
    font-size: 18px;
    color: var(--text-color);
    cursor: pointer;
    padding: 5px;
    border-radius: 4px;
}

.menu-toggle:hover {
    background-color: var(--bg-light);
}

/* Responsive Styles */
@media (max-width: 992px) {
    .sidebar {
        width: var(--sidebar-collapsed-width);
    }

    .sidebar-header h3 {
        font-size: 16px;
    }

    .profile-info {
        display: none;
    }

    .menu-link span {
        display: none;
    }

    .menu-link {
        justify-content: center;
        padding: 12px 0;
    }

    .menu-link i {
        margin-right: 0;
        font-size: 18px;
    }

    .badge {
        position: absolute;
        top: 5px;
        right: 5px;
    }

    .main-content {
        margin-left: var(--sidebar-collapsed-width);
    }
}

@media (max-width: 768px) {
    .sidebar {
        width: 0;
        overflow: hidden;
    }

    .sidebar.active {
        width: var(--sidebar-width);
    }

    .main-content {
        margin-left: 0;
    }

    .menu-toggle {
        display: block;
    }

    .profile-info {
        display: block;
    }

    .menu-link span {
        display: block;
    }

    .menu-link {
        justify-content: flex-start;
        padding: 10px 15px;
    }

    .menu-link i {
        margin-right: 10px;
    }
}
